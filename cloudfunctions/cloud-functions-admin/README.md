# 管理端云函数 (cloud-functions-admin)

面向后台管理系统的云函数服务，提供管理员功能相关的API接口。

## 📁 项目结构

```
cloud-functions-admin/
├── db/                      # 数据库操作层
│   ├── base.js              # 基础数据库类
│   ├── cache.js             # 缓存操作
│   ├── check-ins.js         # 签到数据操作
│   ├── fishing-status.js    # 摸鱼状态数据
│   ├── friend-apps.js       # 友情应用数据
│   ├── index.js             # 数据库入口
│   ├── points-records.js    # 积分记录数据
│   ├── redemption-codes.js  # 兑换码数据
│   ├── store-items.js       # 商品数据
│   ├── user-data.js         # 用户数据存储
│   ├── users.js             # 用户基础数据
│   └── vip-records.js       # VIP记录数据
├── utils/                   # 工具函数
│   ├── date.js              # 日期处理工具
│   ├── response.js          # 响应格式化
│   └── string.js            # 字符串处理工具
└── index.js                 # 云函数入口
```

## 🔌 API接口列表

### 用户管理
- `getUserList`        - 获取用户列表
- `getUserDetail`      - 获取用户详情
- `updateUserStatus`   - 更新用户状态
- `updateUserAdmin`    - 更新用户信息（别名）
- `deleteUser`         - 删除用户（硬删除）
- `getUserStats`       - 获取用户统计
- `exportUserData`     - 导出用户数据

### 公告管理
- `createAnnouncement`        - 创建公告
- `updateAnnouncement`        - 更新公告
- `deleteAnnouncement`        - 删除公告
- `getAnnouncementsAdmin`     - 获取公告列表
- `getAnnouncementStatsAdmin` - 获取公告统计

### 反馈管理
- `getFeedbackListAdmin`      - 获取反馈列表
- `getFeedbackDetailAdmin`    - 获取反馈详情
- `updateFeedbackStatusAdmin` - 更新反馈状态
- `replyFeedbackAdmin`        - 回复反馈
- `deleteFeedbackAdmin`       - 删除反馈
- `getFeedbackStatsAdmin`     - 获取反馈统计

### 积分管理
- `getPointsRecordsAdmin` - 获取积分记录
- `adjustUserPointsAdmin` - 调整用户积分
- `getPointsStatsAdmin`   - 获取积分统计
- `exportPointsDataAdmin` - 导出积分数据

### 商店管理
- `createStoreItem`           - 创建商品
- `updateStoreItem`           - 更新商品
- `deleteStoreItem`           - 删除商品
- `getStoreItemsAdmin`        - 获取商品列表
- `getStoreStatsAdmin`        - 获取商店统计
- `createRedemptionCode`      - 创建兑换码
- `getRedemptionCodesAdmin`   - 获取兑换码列表
- `updateRedemptionCodeAdmin` - 更新兑换码
- `deleteRedemptionCodeAdmin` - 删除兑换码

### 签到管理
- `getCheckInRecordsAdmin` - 获取签到记录
- `getCheckInLeaderboard`  - 获取签到排行榜
- `getCheckInConfig`       - 获取签到配置
- `updateCheckInConfig`    - 更新签到配置
- `getCheckInStatsAdmin`   - 获取签到统计

### 友情应用管理
- `createFriendApp`           - 创建友情应用
- `updateFriendApp`           - 更新友情应用
- `deleteFriendApp`           - 删除友情应用
- `getFriendAppListAdmin`     - 获取友情应用列表
- `getFriendAppStatsAdmin`    - 获取友情应用统计
- `toggleFriendAppVisibility` - 切换应用显示状态

### 摸鱼状态管理
- `getFishingRecordsAdmin`      - 获取摸鱼记录
- `getFishingStatsAdmin`        - 获取摸鱼统计
- `getFishingLeaderboard`       - 获取摸鱼排行榜
- `cleanupExpiredFishingStatus` - 清理过期摸鱼状态
- `endFishingStatusAdmin`       - 结束用户摸鱼状态

### VIP管理
- `getVipUsersAdmin`   - 获取VIP用户列表
- `getVipRecordsAdmin` - 获取VIP记录
- `getVipStatsAdmin`   - 获取VIP统计
- `grantVipAdmin`      - 赠送VIP
- `extendVipAdmin`     - 延期VIP
- `cancelVipAdmin`     - 取消VIP

### 缓存管理
- `getCacheListAdmin`        - 获取缓存列表
- `getCacheStatsAdmin`       - 获取缓存统计
- `getCacheConfigAdmin`      - 获取缓存配置
- `updateCacheConfigAdmin`   - 更新缓存配置
- `cleanupExpiredCacheAdmin` - 清理过期缓存
- `clearAllCacheAdmin`       - 清空所有缓存
- `refreshCacheAdmin`        - 刷新缓存
- `deleteCacheAdmin`         - 删除缓存
- `batchRefreshCacheAdmin`   - 批量刷新缓存
- `batchDeleteCacheAdmin`    - 批量删除缓存

### 系统管理
- `getSystemStatsAdmin`     - 获取系统统计
- `exportDataAdmin`         - 导出数据
- `getSystemConfigAdmin`    - 获取系统配置
- `updateSystemConfigAdmin` - 更新系统配置

## 🔧 开发规范

### 1. 权限验证

#### SECRET_KEY验证
```javascript
// 管理端API必须验证SECRET_KEY
function validateSecretKey(secretKey) {
  const validSecretKey = process.env.ADMIN_SECRET_KEY || 'your-secret-key';
  
  if (!secretKey || secretKey !== validSecretKey) {
    throw new Error('无效的密钥');
  }
}

// 在每个管理端API中使用
exports.main = async (event, context) => {
  try {
    // 验证密钥
    validateSecretKey(event.secretKey);
    
    // 处理API请求
    const { action, data } = event;
    // ...
  } catch (error) {
    return error('权限验证失败', 'UNAUTHORIZED');
  }
};
```

### 2. API设计规范

#### 请求格式
```javascript
// 管理端API调用
const result = await callCloudFunction('cloud-functions-admin', {
  secretKey: 'your-secret-key',  // 必需的密钥
  action: 'getUsersAdmin',       // API名称
  data: {                        // 请求参数
    page: 1,
    pageSize: 20,
    keyword: 'search'
  }
});
```

#### 响应格式
```javascript
// 成功响应（带分页）
{
  success: true,
  message: '操作成功',
  data: [
    // 数据列表
  ],
  pagination: {
    page: 1,
    pageSize: 20,
    total: 100
  },
  timestamp: '2025-08-13T00:00:00.000Z'
}

// 统计响应
{
  success: true,
  message: '获取统计成功',
  data: {
    totalUsers: 1000,
    activeUsers: 800,
    newUsersToday: 50
  },
  timestamp: '2025-08-13T00:00:00.000Z'
}
```

### 3. 数据验证规范

#### 管理员操作验证
```javascript
function validateAdminOperation(data) {
  // 验证必需参数
  if (!data.userId && !data.id) {
    throw new Error('缺少必需的ID参数');
  }
  
  // 验证操作权限
  if (data.action === 'delete' && !data.confirmDelete) {
    throw new Error('删除操作需要确认');
  }
  
  // 验证数据格式
  if (data.email && !isValidEmail(data.email)) {
    throw new Error('邮箱格式不正确');
  }
}
```

#### 批量操作验证
```javascript
function validateBatchOperation(data) {
  if (!Array.isArray(data.ids) || data.ids.length === 0) {
    throw new Error('批量操作需要提供ID列表');
  }
  
  if (data.ids.length > 100) {
    throw new Error('批量操作数量不能超过100个');
  }
  
  // 验证每个ID的格式
  data.ids.forEach(id => {
    if (!id || typeof id !== 'string') {
      throw new Error('ID格式不正确');
    }
  });
}
```

### 4. 数据库操作规范

#### 分页查询
```javascript
async function getPagedData(collection, query, options = {}) {
  const {
    page = 1,
    pageSize = 20,
    orderBy = { field: 'createTime', order: 'desc' },
    fields = null
  } = options;
  
  const skip = (page - 1) * pageSize;
  
  // 获取总数
  const countResult = await db.collection(collection).where(query).count();
  const total = countResult.total;
  
  // 构建查询
  let dataQuery = db.collection(collection).where(query);
  
  if (fields) {
    dataQuery = dataQuery.field(fields);
  }
  
  if (orderBy) {
    dataQuery = dataQuery.orderBy(orderBy.field, orderBy.order);
  }
  
  if (skip > 0) {
    dataQuery = dataQuery.skip(skip);
  }
  
  const result = await dataQuery.limit(pageSize).get();
  
  return {
    data: result.data,
    pagination: {
      page,
      pageSize,
      total
    }
  };
}
```

#### 统计查询
```javascript
async function getStatistics(collection, conditions = {}) {
  const { period = '30d', groupBy = null } = conditions;
  
  // 计算时间范围
  const now = new Date();
  const days = parseInt(period.replace('d', ''));
  const startDate = new Date(now.getTime() - days * 24 * 60 * 60 * 1000);
  
  let query = {
    createTime: db.command.gte(startDate.toISOString())
  };
  
  // 添加其他条件
  Object.assign(query, conditions.where || {});
  
  // 获取数据
  const result = await db.collection(collection).where(query).get();
  
  // 根据需要进行分组统计
  if (groupBy) {
    return groupStatistics(result.data, groupBy);
  }
  
  return {
    total: result.data.length,
    data: result.data
  };
}
```

### 5. 安全规范

#### 敏感数据处理
```javascript
function sanitizeUserData(userData) {
  // 移除敏感字段
  const { openid, ...safeData } = userData;
  
  // 脱敏处理
  if (safeData.phone) {
    safeData.phone = safeData.phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
  }
  
  if (safeData.email) {
    safeData.email = safeData.email.replace(/(.{2}).*(@.*)/, '$1***$2');
  }
  
  return safeData;
}
```

#### 操作日志记录
```javascript
async function logAdminOperation(operation) {
  const logData = {
    adminId: operation.adminId,
    action: operation.action,
    target: operation.target,
    targetId: operation.targetId,
    changes: operation.changes,
    ip: operation.ip,
    userAgent: operation.userAgent,
    timestamp: new Date().toISOString()
  };
  
  await db.collection('admin_logs').add({ data: logData });
}
```

### 6. 错误处理规范

#### 管理端错误码
- `UNAUTHORIZED`           - 权限不足
- `INVALID_SECRET_KEY`     - 密钥无效
- `RESOURCE_NOT_FOUND`     - 资源不存在
- `OPERATION_FORBIDDEN`    - 操作被禁止
- `BATCH_OPERATION_FAILED` - 批量操作失败
- `DATA_EXPORT_ERROR`      - 数据导出错误

#### 错误处理示例
```javascript
try {
  // 验证权限
  validateSecretKey(event.secretKey);
  
  // 执行操作
  const result = await performAdminOperation(event.data);
  
  // 记录操作日志
  await logAdminOperation({
    action: event.action,
    target: 'user',
    targetId: event.data.userId,
    adminId: 'admin'
  });
  
  return success(result, '操作成功');
  
} catch (error) {
  console.error('管理操作失败:', error);
  
  // 根据错误类型返回不同的错误码
  if (error.message.includes('权限')) {
    return error('权限不足', 'UNAUTHORIZED');
  } else if (error.message.includes('不存在')) {
    return error('资源不存在', 'RESOURCE_NOT_FOUND');
  } else {
    return error('操作失败', 'OPERATION_ERROR');
  }
}
```

## 🧪 测试规范

### 1. 权限测试
```javascript
describe('权限验证', () => {
  test('无效密钥应该被拒绝', async () => {
    const result = await callAdminAPI('getUsersAdmin', {
      secretKey: 'invalid-key'
    });
    expect(result.success).toBe(false);
    expect(result.code).toBe('UNAUTHORIZED');
  });
  
  test('有效密钥应该通过', async () => {
    const result = await callAdminAPI('getUsersAdmin', {
      secretKey: 'valid-secret-key'
    });
    expect(result.success).toBe(true);
  });
});
```

### 2. 数据操作测试
```javascript
describe('用户管理', () => {
  test('创建用户', async () => {
    const userData = {
      nickname: '测试用户',
      email: '<EMAIL>'
    };
    
    const result = await createUserAdmin(userData);
    expect(result.success).toBe(true);
    expect(result.data.id).toBeDefined();
  });
  
  test('批量删除用户', async () => {
    const userIds = ['user1', 'user2', 'user3'];
    
    const result = await batchDeleteUsersAdmin({ ids: userIds });
    expect(result.success).toBe(true);
    expect(result.data.deletedCount).toBe(3);
  });
});
```

## 🚀 部署和监控

### 1. 环境配置
```javascript
// 环境变量配置
const config = {
  ADMIN_SECRET_KEY: process.env.ADMIN_SECRET_KEY,
  DATABASE_ENV: process.env.DATABASE_ENV || 'prod',
  LOG_LEVEL: process.env.LOG_LEVEL || 'info',
  CACHE_TTL: parseInt(process.env.CACHE_TTL) || 3600
};
```

### 2. 监控指标
- 管理员操作频率
- API调用成功率
- 数据导出性能
- 批量操作耗时
- 权限验证失败次数

### 3. 安全监控
- 异常登录尝试
- 敏感操作记录
- 数据访问模式
- 权限提升尝试

## 📝 更新日志

### v1.0.0 (2025-08-13)
- 初始版本发布
- 完整的管理端API
- 权限验证和安全控制
- 数据导出和统计功能
