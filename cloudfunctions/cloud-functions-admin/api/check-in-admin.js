/**
 * 签到管理API（管理端）
 * 提供签到数据统计和管理功能
 */

const { success, error, paginated, statsData, invalidParam, exportData } = require('../utils/response')
const { wrapAsync } = require('../utils/async-wrapper')
const { validateRequired, validatePagination, validateSorting, validateDateRange, validateEnum, validateId } = require('../utils/validators')

// 使用本地的数据库操作类
const BaseDB = require('../db/base')
const cloud = require('wx-server-sdk')

// 初始化云开发
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV,
})

const db = cloud.database()

// 签到记录数据库操作类
class CheckInDB extends BaseDB {
  constructor() {
    super('check-ins')
  }

  /**
   * 获取签到统计信息（管理端）
   * @param {string} period - 统计周期
   * @returns {Promise<Object>} 统计结果
   */
  async getCheckInStatsAdmin(period = '30d') {
    try {
      console.log(`[CheckInDB] 获取签到统计信息: ${period}`)

      // 计算时间范围
      let startDate = null
      const now = new Date()
      
      switch (period) {
        case '7d':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
          break
        case '30d':
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
          break
        case '90d':
          startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
          break
        case '1y':
          startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000)
          break
        case 'all':
        default:
          startDate = null
          break
      }

      const baseWhere = {}
      if (startDate) {
        baseWhere.createTime = { $gte: startDate }
      }

      // 总签到次数
      const totalResult = await this.count(baseWhere)
      const total = totalResult.success ? totalResult.data : 0

      // 今日签到次数
      const today = new Date()
      today.setHours(0, 0, 0, 0)
      const todayResult = await this.count({ 
        createTime: { $gte: today }
      })
      const todayCount = todayResult.success ? todayResult.data : 0

      const stats = {
        total,
        todayCount,
        averageDaily: period !== 'all' ? Math.round(total / parseInt(period)) : 0,
        period,
        generatedAt: new Date().toISOString()
      }

      console.log('[CheckInDB] 签到统计信息获取成功')

      return {
        success: true,
        data: stats
      }
    } catch (error) {
      console.error('[CheckInDB] 获取签到统计失败:', error)
      return { 
        success: false, 
        message: error.message 
      }
    }
  }

  /**
   * 获取用户签到历史（管理端）
   * @param {Object} options - 查询选项
   * @returns {Promise<Object>} 查询结果
   */
  async getUserCheckInHistoryAdmin(options = {}) {
    try {
      const {
        userId = 'all',
        startDate,
        endDate,
        sortBy = 'createTime',
        sortOrder = 'desc',
        limit = 20,
        skip = 0
      } = options

      console.log(`[CheckInDB] 管理端获取签到历史: userId=${userId}`)

      // 构建查询条件
      const where = {}
      
      if (userId !== 'all') {
        where.userId = userId
      }
      
      if (startDate || endDate) {
        where.createTime = {}
        if (startDate) {
          where.createTime.$gte = startDate
        }
        if (endDate) {
          where.createTime.$lte = endDate
        }
      }

      // 查询总数
      const countResult = await this.count(where)
      const total = countResult.success ? countResult.data : 0

      // 查询数据
      const dataResult = await this.find(where, {
        limit,
        skip,
        orderBy: sortBy,
        order: sortOrder
      })

      if (!dataResult.success) {
        return dataResult
      }

      console.log(`[CheckInDB] 管理端签到历史查询成功，共 ${dataResult.data.length} 条记录，总计 ${total} 条`)

      return {
        success: true,
        data: {
          list: dataResult.data,
          total,
          hasMore: skip + dataResult.data.length < total
        }
      }
    } catch (error) {
      console.error('[CheckInDB] 管理端获取签到历史失败:', error)
      return { 
        success: false, 
        message: error.message 
      }
    }
  }

  /**
   * 导出签到数据（管理端）
   * @param {Object} options - 导出选项
   * @returns {Promise<Object>} 导出结果
   */
  async exportCheckInDataAdmin(options = {}) {
    try {
      const {
        userId = 'all',
        startDate,
        endDate,
        limit = 1000
      } = options

      console.log(`[CheckInDB] 导出签到数据: userId=${userId}, limit=${limit}`)

      // 构建查询条件
      const where = {}
      
      if (userId !== 'all') {
        where.userId = userId
      }
      
      if (startDate || endDate) {
        where.createTime = {}
        if (startDate) {
          where.createTime.$gte = startDate
        }
        if (endDate) {
          where.createTime.$lte = endDate
        }
      }

      // 查询数据
      const result = await this.find(where, {
        orderBy: 'createTime',
        order: 'desc',
        limit
      })

      if (!result.success) {
        return result
      }

      console.log(`[CheckInDB] 签到数据导出成功，共 ${result.data.length} 条记录`)

      return {
        success: true,
        data: result.data
      }
    } catch (error) {
      console.error('[CheckInDB] 导出签到数据失败:', error)
      return {
        success: false,
        message: error.message
      }
    }
  }

  /**
   * 获取用户签到历史（包含统计数据）
   */
  async getUserCheckInHistoryWithStats(options = {}) {
    try {
      const { userId, startDate, endDate } = options

      console.log(`[CheckInDB] 获取用户签到历史: userId=${userId}`)

      // 构建查询条件
      const where = { userId: userId }
      console.log(`[CheckInDB] 签到记录查询条件:`, JSON.stringify(where))

      // 时间范围筛选
      if (startDate || endDate) {
        const timeFilter = {}
        if (startDate) timeFilter.$gte = new Date(startDate)
        if (endDate) timeFilter.$lte = new Date(endDate)
        where.date = timeFilter
      }

      // 查询签到记录
      console.log(`[CheckInDB] 签到记录最终查询条件:`, JSON.stringify(where))
      const records = await this.collection
        .where(where)
        .orderBy('date', 'desc')
        .get()

      console.log(`[CheckInDB] 签到记录查询结果数量:`, records.data.length)

      // 计算统计数据
      const now = new Date()
      const thisMonthStart = new Date(now.getFullYear(), now.getMonth(), 1)
      const thisMonthEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0)

      // 本月签到天数
      const thisMonthRecords = records.data.filter(record => {
        const recordDate = new Date(record.date)
        return recordDate >= thisMonthStart && recordDate <= thisMonthEnd
      })

      // 连续签到天数（从最近开始计算）
      let consecutiveDays = 0
      const sortedRecords = records.data.sort((a, b) => new Date(b.date) - new Date(a.date))

      for (let i = 0; i < sortedRecords.length; i++) {
        const recordDate = new Date(sortedRecords[i].date)
        const expectedDate = new Date(now)
        expectedDate.setDate(expectedDate.getDate() - i)

        // 检查日期是否连续
        if (recordDate.toDateString() === expectedDate.toDateString()) {
          consecutiveDays++
        } else {
          break
        }
      }

      const stats = {
        thisMonth: thisMonthRecords.length,
        consecutive: consecutiveDays,
        total: records.data.length
      }

      return {
        success: true,
        data: {
          records: records.data,
          stats
        }
      }
    } catch (error) {
      console.error('[CheckInDB] 获取用户签到历史失败:', error)
      return {
        success: false,
        message: error.message || '获取用户签到历史失败'
      }
    }
  }
}

const checkInDB = new CheckInDB()

/**
 * 获取签到统计（管理端）
 */
exports.getCheckInStatsAdmin = wrapAsync(async (params = {}) => {
  const { period = '30d' } = params

  // 验证时间周期
  const validPeriods = ['7d', '30d', '90d', '1y', 'all']
  const periodValidation = validateEnum(period, 'period', validPeriods)
  if (!periodValidation.success) {
    return periodValidation
  }

  try {
    const result = await checkInDB.getCheckInStatsAdmin(period)
    
    if (!result.success) {
      return error(result.message || '获取签到统计失败')
    }

    return statsData(result.data, '获取签到统计成功')
  } catch (err) {
    console.error('获取签到统计失败:', err)
    return error('获取签到统计失败')
  }
})

/**
 * 获取用户签到历史（管理端）
 */
exports.getUserCheckInHistoryAdmin = wrapAsync(async (params = {}) => {
  const {
    userId = 'all',
    startDate,
    endDate,
    page = 1,
    pageSize = 20,
    sortBy = 'createTime',
    sortOrder = 'desc'
  } = params

  // 验证分页参数
  const paginationValidation = validatePagination(params, 100)
  if (!paginationValidation.success) {
    return paginationValidation
  }

  // 验证排序参数
  const allowedSortFields = ['createTime', 'userId']
  const sortingValidation = validateSorting(params, allowedSortFields)
  if (!sortingValidation.success) {
    return sortingValidation
  }

  // 验证日期范围
  const dateValidation = validateDateRange(params)
  if (!dateValidation.success) {
    return dateValidation
  }

  try {
    const result = await checkInDB.getUserCheckInHistoryAdmin({
      userId,
      startDate: dateValidation.data.startDate,
      endDate: dateValidation.data.endDate,
      sortBy,
      sortOrder,
      limit: paginationValidation.data.limit,
      skip: paginationValidation.data.skip
    })

    if (!result.success) {
      return error(result.message || '获取签到历史失败')
    }

    return paginated(
      result.data.list,
      result.data.total,
      page,
      pageSize,
      '获取签到历史成功'
    )
  } catch (err) {
    console.error('获取签到历史失败:', err)
    return error('获取签到历史失败')
  }
})

/**
 * 导出签到数据（管理端）
 */
exports.exportCheckInData = wrapAsync(async (params = {}) => {
  const {
    format = 'json',
    userId = 'all',
    startDate,
    endDate,
    limit = 1000
  } = params

  // 验证导出格式
  const validFormats = ['json', 'csv']
  const formatValidation = validateEnum(format, 'format', validFormats)
  if (!formatValidation.success) {
    return formatValidation
  }

  // 验证导出数量限制
  if (limit > 10000) {
    return invalidParam('limit', '单次导出不能超过10000条记录')
  }

  // 验证日期范围
  const dateValidation = validateDateRange(params)
  if (!dateValidation.success) {
    return dateValidation
  }

  try {
    const result = await checkInDB.exportCheckInDataAdmin({
      userId,
      startDate: dateValidation.data.startDate,
      endDate: dateValidation.data.endDate,
      limit
    })

    if (!result.success) {
      return error(result.message || '导出签到数据失败')
    }

    // 生成文件名
    const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-')
    const filename = `checkin_export_${timestamp}.${format}`

    return exportData(result.data, filename, format, '签到数据导出成功')
  } catch (err) {
    console.error('导出签到数据失败:', err)
    return error('导出签到数据失败')
  }
})

/**
 * 获取用户签到历史（包含统计数据）
 */
exports.getUserCheckInHistoryWithStatsAdmin = wrapAsync(async (params = {}) => {
  console.log('[getUserCheckInHistoryWithStatsAdmin] 接收到的参数:', JSON.stringify(params))

  const { userId, startDate, endDate } = params

  // 验证必需参数
  const requiredValidation = validateRequired(params, ['userId'])
  if (!requiredValidation.success) {
    console.log('[getUserCheckInHistoryWithStatsAdmin] 参数验证失败:', requiredValidation)
    return requiredValidation
  }

  // 验证用户ID
  const userIdValidation = validateId(userId, 'userId')
  if (!userIdValidation.success) {
    return userIdValidation
  }

  try {
    const result = await checkInDB.getUserCheckInHistoryWithStats({
      userId,
      startDate,
      endDate
    })

    if (!result.success) {
      return error(result.message || '获取用户签到历史失败')
    }

    return success(result.data, '获取用户签到历史成功')
  } catch (err) {
    console.error('获取用户签到历史失败:', err)
    return error('获取用户签到历史失败', 'GET_USER_CHECKIN_HISTORY_ERROR', err.message)
  }
})
