/**
 * 用户数据库操作类（管理端）
 */

const BaseDB = require('./base')
const { processUsersAvatars, processUserAvatar } = require('../utils/file')
const cloud = require('wx-server-sdk')

// 初始化云开发
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV,
})

const db = cloud.database()

class UsersDB extends BaseDB {
  constructor() {
    super('users')
  }

  /**
   * 获取用户列表（管理端）
   * @param {Object} options - 查询选项
   * @returns {Promise<Object>} 查询结果
   */
  async getUserListAdmin(options = {}) {
    try {
      const {
        status = 'all',
        vipStatus = 'all',
        keyword = '',
        startDate,
        endDate,
        sortBy = 'createTime',
        sortOrder = 'desc',
        limit = 20,
        skip = 0
      } = options

      console.log(`[UsersDB] 管理端获取用户列表: status=${status}, vipStatus=${vipStatus}, keyword=${keyword}`)

      // 构建查询条件
      const where = {}

      // 用户状态筛选
      if (status !== 'all') {
        where.status = status
      }

      if (vipStatus !== 'all') {
        where['vip.status'] = vipStatus === 'true' || vipStatus === true
      }
      
      if (keyword) {
        where.$or = [
          { nickname: new RegExp(keyword, 'i') },
          { username: new RegExp(keyword, 'i') },
          { email: new RegExp(keyword, 'i') }
        ]
      }
      
      if (startDate || endDate) {
        where.createTime = {}
        if (startDate) {
          where.createTime.$gte = startDate
        }
        if (endDate) {
          where.createTime.$lte = endDate
        }
      }

      // 查询总数
      const countResult = await this.count(where)
      const total = countResult.success ? countResult.data : 0

      // 查询数据
      const dataResult = await this.find(where, {
        limit,
        skip,
        orderBy: sortBy,
        order: sortOrder
      })

      if (!dataResult.success) {
        return dataResult
      }

      console.log(`[UsersDB] 管理端用户查询成功，共 ${dataResult.data.length} 条记录，总计 ${total} 条`)

      // 处理头像URL
      const processedUsers = await processUsersAvatars(dataResult.data)

      return {
        success: true,
        data: {
          list: processedUsers,
          total,
          hasMore: skip + dataResult.data.length < total
        }
      }
    } catch (error) {
      console.error('[UsersDB] 管理端获取用户列表失败:', error)
      return { 
        success: false, 
        message: error.message 
      }
    }
  }

  /**
   * 获取用户详情（管理端）
   * @param {string} userId - 用户ID
   * @returns {Promise<Object>} 查询结果
   */
  async getUserDetailAdmin(userId) {
    try {
      console.log(`[UsersDB] 获取用户详情: ${userId}`)

      const result = await this.findById(userId)

      if (result.success && result.data) {
        // 处理头像URL
        result.data = await processUserAvatar(result.data)
      }

      return result
    } catch (error) {
      console.error(`[UsersDB] 获取用户详情失败: ${userId}`, error)
      return { 
        success: false, 
        message: error.message 
      }
    }
  }

  /**
   * 更新用户状态（管理端）
   * @param {string} userId - 用户ID
   * @param {Object} updateData - 更新数据
   * @returns {Promise<Object>} 更新结果
   */
  async updateUserStatusAdmin(userId, updateData) {
    try {
      console.log(`[UsersDB] 更新用户状态: ${userId}`)
      
      const result = await this.update({ _id: userId }, updateData)
      
      if (result.success) {
        console.log(`[UsersDB] 用户状态更新成功: ${userId}`)
      }
      
      return result
    } catch (error) {
      console.error(`[UsersDB] 更新用户状态失败: ${userId}`, error)
      return { 
        success: false, 
        message: error.message 
      }
    }
  }

  /**
   * 获取用户统计信息（管理端）
   * @param {string} period - 统计周期
   * @returns {Promise<Object>} 统计结果
   */
  async getUserStatsAdmin(period = '30d') {
    try {
      console.log(`[UsersDB] 获取用户统计信息: ${period}`)

      // 计算时间范围
      let startDate = null
      const now = new Date()
      
      switch (period) {
        case '7d':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
          break
        case '30d':
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
          break
        case '90d':
          startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
          break
        case '1y':
          startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000)
          break
        case 'all':
        default:
          startDate = null
          break
      }

      const baseWhere = {}
      if (startDate) {
        baseWhere.createTime = { $gte: startDate }
      }

      // 总用户数
      const totalResult = await this.count(baseWhere)
      const total = totalResult.success ? totalResult.data : 0

      // 正常用户数
      const activeResult = await this.count({ ...baseWhere, status: 'active' })
      const active = activeResult.success ? activeResult.data : 0

      // 屏蔽用户数
      const blockedResult = await this.count({ ...baseWhere, status: 'inactive' })
      const blocked = blockedResult.success ? blockedResult.data : 0

      // 封禁用户数
      const bannedResult = await this.count({ ...baseWhere, status: 'banned' })
      const banned = bannedResult.success ? bannedResult.data : 0

      // VIP用户数
      const vipResult = await this.count({ ...baseWhere, 'vip.status': true })
      const vip = vipResult.success ? vipResult.data : 0

      // 今日新增用户数
      const todayStart = new Date()
      todayStart.setHours(0, 0, 0, 0)
      const todayNewResult = await this.count({ createTime: { $gte: todayStart } })
      const todayNew = todayNewResult.success ? todayNewResult.data : 0

      const stats = {
        total,
        active,
        blocked,
        banned,
        vip,
        todayNew,
        period,
        generatedAt: new Date().toISOString()
      }

      console.log('[UsersDB] 用户统计信息获取成功')

      return {
        success: true,
        data: stats
      }
    } catch (error) {
      console.error('[UsersDB] 获取用户统计失败:', error)
      return {
        success: false,
        message: error.message
      }
    }
  }

  /**
   * 按日期范围获取用户统计
   * @param {Date} startDate - 开始日期
   * @param {Date} endDate - 结束日期
   * @returns {Promise<Object>} 统计结果
   */
  async getUserStatsByDateRange(startDate, endDate) {
    try {
      console.log(`[UsersDB] 按日期范围获取用户统计: ${startDate.toISOString()} - ${endDate.toISOString()}`)

      const query = {
        createTime: {
          $gte: startDate.toISOString(),
          $lt: endDate.toISOString()
        }
      }

      const result = await this.findMany(query)

      if (result.success) {
        const users = result.data
        const stats = {
          total: users.length,
          vipUsers: users.filter(user => user.vip?.status).length,
          newUsers: users.length, // 在这个时间范围内都是新用户
          totalPoints: users.reduce((sum, user) => sum + (user.points || 0), 0)
        }

        return {
          success: true,
          data: stats
        }
      }

      return result
    } catch (error) {
      console.error('[UsersDB] 按日期范围获取用户统计失败:', error)
      return {
        success: false,
        message: error.message
      }
    }
  }

  /**
   * 获取VIP用户列表（管理端）
   */
  async getVipUsersAdmin(options = {}) {
    try {
      const {
        status = 'all', // all, active, expired
        keyword = '',
        startDate,
        endDate,
        sortBy = 'vipExpireAt',
        sortOrder = 'desc',
        page = 1,
        pageSize = 20
      } = options

      console.log(`[UsersDB] 获取VIP用户列表: status=${status}, keyword=${keyword}`)

      // 构建查询条件
      const where = {}

      // VIP状态筛选
      if (status === 'active') {
        where['vip.status'] = true
        where['vip.expireAt'] = { $gt: new Date() }
      } else if (status === 'expired') {
        where.$or = [
          { 'vip.status': false },
          { 'vip.expireAt': { $lte: new Date() } }
        ]
      } else {
        // 只查询曾经是VIP的用户
        where['vip.expireAt'] = { $exists: true }
      }

      // 关键词搜索
      if (keyword) {
        where.$and = where.$and || []
        where.$and.push({
          $or: [
            { nickname: new RegExp(keyword, 'i') },
            { openid: new RegExp(keyword, 'i') }
          ]
        })
      }

      // 时间范围筛选
      if (startDate || endDate) {
        const timeFilter = {}
        if (startDate) timeFilter.$gte = new Date(startDate)
        if (endDate) timeFilter.$lte = new Date(endDate)
        where['vip.grantTime'] = timeFilter
      }

      // 计算分页
      const skip = (page - 1) * pageSize

      // 构建排序
      const orderBy = {}
      orderBy[sortBy] = sortOrder === 'desc' ? -1 : 1

      // 查询数据
      const [list, total] = await Promise.all([
        this.collection
          .where(where)
          .orderBy(sortBy, sortOrder)
          .skip(skip)
          .limit(pageSize)
          .get(),
        this.collection.where(where).count()
      ])

      // 处理头像URL
      const processedList = await processUsersAvatars(list.data)

      return {
        list: processedList,
        total: total.total
      }
    } catch (error) {
      console.error('[UsersDB] 获取VIP用户列表失败:', error)
      throw error
    }
  }

  /**
   * 获取VIP记录列表（管理端）
   */
  async getVipRecordsAdmin(options = {}) {
    try {
      const {
        userId,
        type = 'all', // all, grant, renew, expire
        startDate,
        endDate,
        sortBy = 'createTime',
        sortOrder = 'desc',
        page = 1,
        pageSize = 20
      } = options

      console.log(`[UsersDB] 获取VIP记录列表: userId=${userId}, type=${type}`)

      // 构建查询条件
      const where = {}
      console.log(`[UsersDB] VIP记录初始查询条件:`, JSON.stringify(where))

      if (userId) {
        // userId 现在直接是用户的 _id
        console.log(`[UsersDB] 使用用户文档ID: _id=${userId}`)
        where.userId = userId
      }

      if (type !== 'all') {
        where.type = type
      }

      // 时间范围筛选
      if (startDate || endDate) {
        const timeFilter = {}
        if (startDate) timeFilter.$gte = new Date(startDate)
        if (endDate) timeFilter.$lte = new Date(endDate)
        where.createTime = timeFilter
      }

      // 计算分页
      const skip = (page - 1) * pageSize

      // 构建排序
      const orderBy = {}
      orderBy[sortBy] = sortOrder === 'desc' ? -1 : 1

      // 查询VIP记录
      console.log(`[UsersDB] VIP记录最终查询条件:`, JSON.stringify(where))
      const vipRecordsCollection = db.collection('vip-records')
      const [list, total] = await Promise.all([
        vipRecordsCollection
          .where(where)
          .orderBy(sortBy, sortOrder)
          .skip(skip)
          .limit(pageSize)
          .get(),
        vipRecordsCollection.where(where).count()
      ])

      console.log(`[UsersDB] VIP记录查询结果: list=${list.data.length}, total=${total.total}`)

      // 如果有记录，获取用户信息
      const processedList = []
      for (const record of list.data) {
        const userInfo = await this.collection
          .where({ _id: record.userId })  // 使用_id查询用户信息
          .field({ nickname: true, avatar: true, openid: true })
          .get()

        processedList.push({
          ...record,
          userInfo: userInfo.data[0] || null
        })
      }

      return {
        list: processedList,
        total: total.total
      }
    } catch (error) {
      console.error('[UsersDB] 获取VIP记录列表失败:', error)
      throw error
    }
  }

  /**
   * 获取VIP统计数据（管理端）
   */
  async getVipStatsAdmin(options = {}) {
    try {
      const { period = '30d' } = options

      console.log(`[UsersDB] 获取VIP统计数据: period=${period}`)

      // 计算时间范围
      const now = new Date()
      const periodDays = {
        '7d': 7,
        '30d': 30,
        '90d': 90,
        '1y': 365
      }
      const days = periodDays[period] || 30
      const startDate = new Date(now.getTime() - days * 24 * 60 * 60 * 1000)

      // 获取当前VIP用户数
      const activeVipCount = await this.collection
        .where({
          'vip.status': true,
          'vip.expireAt': { $gt: now }
        })
        .count()

      // 获取总VIP用户数（曾经是VIP的）
      const totalVipCount = await this.collection
        .where({
          'vip.expireAt': { $exists: true }
        })
        .count()

      // 获取期间内新增VIP数
      const newVipCount = await this.collection
        .where({
          'vip.grantTime': { $gte: startDate }
        })
        .count()

      // 获取期间内过期VIP数
      const expiredVipCount = await this.collection
        .where({
          'vip.expireAt': {
            $gte: startDate,
            $lte: now
          }
        })
        .count()

      // 获取VIP记录统计
      const vipRecordsCollection = db.collection('vip-records')
      const recordsStats = await vipRecordsCollection
        .where({
          createTime: { $gte: startDate }
        })
        .get()

      // 按类型统计记录
      const recordsByType = recordsStats.data.reduce((acc, record) => {
        acc[record.type] = (acc[record.type] || 0) + 1
        return acc
      }, {})

      return {
        activeVipCount: activeVipCount.total,
        totalVipCount: totalVipCount.total,
        newVipCount: newVipCount.total,
        expiredVipCount: expiredVipCount.total,
        recordsByType,
        period,
        startDate,
        endDate: now
      }
    } catch (error) {
      console.error('[UsersDB] 获取VIP统计数据失败:', error)
      throw error
    }
  }

  /**
   * 赠送VIP（管理端）
   */
  async grantVipAdmin(options = {}) {
    try {
      const { userId, duration, reason } = options

      console.log(`[UsersDB] 赠送VIP: userId=${userId}, duration=${duration}天`)

      // 获取用户信息
      const userResult = await this.collection
        .where({ _id: userId })
        .get()

      if (userResult.data.length === 0) {
        return { success: false, message: '用户不存在' }
      }

      const user = userResult.data[0]
      const now = new Date()

      // 计算新的过期时间
      let newExpiredAt
      if (user.vip && user.vip.status && user.vip.expiredAt > now) {
        // 如果用户当前是VIP且未过期，在现有时间基础上延长
        newExpiredAt = new Date(user.vip.expiredAt.getTime() + duration * 24 * 60 * 60 * 1000)
      } else {
        // 如果用户不是VIP或已过期，从现在开始计算
        newExpiredAt = new Date(now.getTime() + duration * 24 * 60 * 60 * 1000)
      }

      // 更新用户VIP状态
      await this.collection
        .where({ _id: userId })
        .update({
          data: {
            'vip.status': true,
            'vip.expiredAt': newExpiredAt,
            'vip.grantTime': user.vip?.grantTime || now,
            'vip.lastGrantTime': now,
            updateTime: now
          }
        })

      // 记录VIP操作
      const vipRecordsCollection = db.collection('vip-records')
      await vipRecordsCollection.add({
        userId: user._id,  // 使用用户文档的_id
        type: user.vip?.status ? 'renew' : 'grant',
        duration,
        reason,
        expiredAt: newExpiredAt,
        createTime: now
      })

      return {
        success: true,
        data: {
          userId,
          expiredAt: newExpiredAt,
          duration
        }
      }
    } catch (error) {
      console.error('[UsersDB] 赠送VIP失败:', error)
      throw error
    }
  }

  /**
   * 取消VIP（管理端）
   */
  async revokeVipAdmin(options = {}) {
    try {
      const { userId, reason } = options

      console.log(`[UsersDB] 取消VIP: userId=${userId}`)

      // 获取用户信息
      const userResult = await this.collection
        .where({ _id: userId })
        .get()

      if (userResult.data.length === 0) {
        return { success: false, message: '用户不存在' }
      }

      const user = userResult.data[0]
      if (!user.vip || !user.vip.status) {
        return { success: false, message: '用户不是VIP' }
      }

      const now = new Date()

      // 更新用户VIP状态
      await this.collection
        .where({ _id: userId })
        .update({
          data: {
            'vip.status': false,
            'vip.revokeTime': now,
            updateTime: now
          }
        })

      // 记录VIP操作
      const vipRecordsCollection = db.collection('vip-records')
      await vipRecordsCollection.add({
        userId: user._id,  // 使用用户文档的_id
        type: 'revoke',
        reason,
        createTime: now
      })

      return {
        success: true,
        data: {
          userId,
          revokeTime: now
        }
      }
    } catch (error) {
      console.error('[UsersDB] 取消VIP失败:', error)
      throw error
    }
  }



  /**
   * 获取用户积分历史（管理端）
   */
  async getUserPointsHistoryAdmin(options = {}) {
    try {
      const {
        userId,
        type = 'all',
        page = 1,
        pageSize = 20,
        startDate,
        endDate
      } = options

      console.log(`[UsersDB] 获取用户积分历史: userId=${userId}, type=${type}`)

      // 构建查询条件
      const where = { userId: userId }
      console.log(`[UsersDB] 积分记录查询条件:`, JSON.stringify(where))

      // 积分类型筛选
      if (type !== 'all') {
        if (type === 'earn') {
          where.amount = { $gt: 0 }
        } else if (type === 'spend') {
          where.amount = { $lt: 0 }
        }
      }

      // 时间范围筛选
      if (startDate || endDate) {
        const timeFilter = {}
        if (startDate) timeFilter.$gte = new Date(startDate)
        if (endDate) timeFilter.$lte = new Date(endDate)
        where.timestamp = timeFilter
      }

      // 计算分页
      const skip = (page - 1) * pageSize

      // 查询积分记录
      console.log(`[UsersDB] 积分记录最终查询条件:`, JSON.stringify(where))
      const pointsCollection = db.collection('points-records')
      const [list, total] = await Promise.all([
        pointsCollection
          .where(where)
          .orderBy('timestamp', 'desc')
          .skip(skip)
          .limit(pageSize)
          .get(),
        pointsCollection.where(where).count()
      ])

      console.log(`[UsersDB] 积分记录查询结果: list=${list.data.length}, total=${total.total}`)

      return {
        list: list.data,
        total: total.total
      }
    } catch (error) {
      console.error('[UsersDB] 获取用户积分历史失败:', error)
      throw error
    }
  }

  /**
   * 删除用户（管理端）
   * 硬删除用户及其在所有相关集合中的数据
   * @param {string} userId - 用户ID (_id)
   * @param {string} reason - 删除原因
   * @returns {Promise<Object>} 删除结果
   */
  async deleteUserAdmin(userId, reason = '管理员删除') {
    try {
      console.log(`[UsersDB] 开始删除用户: ${userId}`)

      // 首先获取用户信息
      const userResult = await this.collection
        .where({ _id: userId })
        .get()

      if (userResult.data.length === 0) {
        return { success: false, message: '用户不存在' }
      }

      const user = userResult.data[0]
      const userDocId = user._id

      console.log(`[UsersDB] 找到用户: ${user.nickname} (${userDocId})`)

      // 需要删除的集合和对应的查询条件
      const collectionsToDelete = [
        { name: 'check-ins', field: 'userId', value: userDocId },
        { name: 'vip-records', field: 'userId', value: userDocId },
        { name: 'points-records', field: 'userId', value: userDocId },
        { name: 'user-data', field: 'userId', value: userDocId },
        { name: 'feedbacks', field: 'userId', value: userDocId },
        { name: 'redemption-codes', field: 'userId', value: userDocId }
      ]

      const deletedCollections = []
      let totalDeleted = 0

      // 删除相关集合中的数据
      for (const collection of collectionsToDelete) {
        try {
          console.log(`[UsersDB] 删除集合 ${collection.name} 中的数据...`)

          const deleteResult = await db.collection(collection.name)
            .where({ [collection.field]: collection.value })
            .remove()

          const deletedCount = deleteResult.stats?.removed || 0
          if (deletedCount > 0) {
            deletedCollections.push({
              collection: collection.name,
              deletedCount
            })
            totalDeleted += deletedCount
            console.log(`[UsersDB] 从 ${collection.name} 删除了 ${deletedCount} 条记录`)
          }
        } catch (error) {
          console.error(`[UsersDB] 删除集合 ${collection.name} 数据失败:`, error)
          // 继续删除其他集合，不因为一个集合失败而停止
        }
      }

      // 最后删除用户记录
      const userDeleteResult = await this.collection
        .where({ _id: userId })
        .remove()

      const userDeleted = userDeleteResult.stats?.removed || 0
      if (userDeleted > 0) {
        deletedCollections.push({
          collection: 'users',
          deletedCount: userDeleted
        })
        totalDeleted += userDeleted
        console.log(`[UsersDB] 删除用户记录: ${userDeleted} 条`)
      }

      console.log(`[UsersDB] 用户删除完成: ${userId}, 总计删除 ${totalDeleted} 条记录`)

      return {
        success: true,
        data: {
          userId,
          userDocId,
          nickname: user.nickname,
          deletedCollections,
          totalDeleted,
          reason
        }
      }
    } catch (error) {
      console.error('[UsersDB] 删除用户失败:', error)
      return {
        success: false,
        message: error.message || '删除用户失败'
      }
    }
  }
}

module.exports = new UsersDB()
