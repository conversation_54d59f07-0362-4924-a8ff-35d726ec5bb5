/**
 * 用户状态检查中间件
 */

const cloud = require('wx-server-sdk')

// 初始化云开发
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV,
})

const db = cloud.database()

/**
 * 白名单接口 - 这些接口不需要进行状态检查
 */
const WHITELIST_APIS = [
  'getOpenId',
  'getMiniProgramCode',
  'getUserInfo',  // 获取用户信息需要在状态检查之前执行
  'getAnnouncementList',  // 公告可以正常查看
  'getAllConfigs'  // 配置信息可以正常获取
]

/**
 * 检查用户状态
 * @param {string} openid - 用户openid
 * @returns {Promise<Object>} 检查结果
 */
async function checkUserStatus(openid) {
  try {
    if (!openid) {
      return {
        success: false,
        message: '用户身份验证失败',
        code: 'NO_OPENID'
      }
    }

    // 查询用户信息
    const userResult = await db.collection('users')
      .where({ openid })
      .field({ status: true, nickname: true })
      .get()

    if (userResult.data.length === 0) {
      return {
        success: false,
        message: '用户不存在',
        code: 'USER_NOT_FOUND'
      }
    }

    const user = userResult.data[0]
    const status = user.status || 'active'  // 默认为正常状态

    console.log(`[UserStatus] 用户状态检查: ${user.nickname} (${openid}) - ${status}`)

    // 检查用户状态
    switch (status) {
      case 'active':
        // 正常状态，允许访问
        return {
          success: true,
          status: 'active',
          message: '用户状态正常'
        }

      case 'inactive':
        // 屏蔽状态，阻止所有云函数调用
        return {
          success: false,
          status: 'inactive',
          message: '数据异常（-1）',
          code: 'USER_BLOCKED'
        }

      case 'banned':
        // 封禁状态，阻止所有云函数调用
        return {
          success: false,
          status: 'banned',
          message: '数据异常（-2）',
          code: 'USER_BANNED'
        }

      default:
        // 未知状态，按屏蔽处理
        return {
          success: false,
          status: 'unknown',
          message: '数据异常（-3）',
          code: 'USER_STATUS_UNKNOWN'
        }
    }
  } catch (error) {
    console.error('[UserStatus] 用户状态检查失败:', error)
    return {
      success: false,
      message: '状态检查失败，请稍后重试',
      code: 'STATUS_CHECK_ERROR'
    }
  }
}

/**
 * 用户状态检查中间件
 * @param {string} apiType - API类型
 * @param {Object} event - 云函数事件对象
 * @returns {Promise<Object>} 检查结果
 */
async function validateUserStatus(apiType, event) {
  try {
    // 检查是否为白名单接口
    if (WHITELIST_APIS.includes(apiType)) {
      console.log(`[UserStatus] 白名单接口，跳过状态检查: ${apiType}`)
      return {
        success: true,
        message: '白名单接口'
      }
    }

    // 获取用户openid
    const wxContext = cloud.getWXContext()
    const openid = wxContext.OPENID

    if (!openid) {
      return {
        success: false,
        message: '用户身份验证失败',
        code: 'NO_OPENID'
      }
    }

    // 检查用户状态
    const statusResult = await checkUserStatus(openid)
    
    if (!statusResult.success) {
      console.warn(`[UserStatus] 用户状态检查失败: ${openid} - ${statusResult.message}`)
      return statusResult
    }

    console.log(`[UserStatus] 用户状态检查通过: ${openid}`)
    return {
      success: true,
      message: '用户状态正常'
    }

  } catch (error) {
    console.error('[UserStatus] 状态检查中间件执行失败:', error)
    return {
      success: false,
      message: '状态检查失败，请稍后重试',
      code: 'MIDDLEWARE_ERROR'
    }
  }
}

module.exports = {
  validateUserStatus,
  checkUserStatus,
  WHITELIST_APIS
}
