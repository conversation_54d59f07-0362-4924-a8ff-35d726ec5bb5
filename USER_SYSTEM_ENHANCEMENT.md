# 用户系统完善文档

## 📋 概述

本文档记录了对用户系统的完善工作，包括移除 isAdmin 字段、完善用户状态系统、添加删除用户功能等。

## 🔄 主要变更

### 1. 移除 isAdmin 字段

**原因**: 用户不区分是否是管理员，管理员是通过后台管理系统单独管理的。

**影响的文件**:
- `cloudfunctions/cloud-functions/db/users.js` - 移除用户创建时的 isAdmin 默认值
- `cloudfunctions/cloud-functions-admin/db/users.js` - 移除查询条件中的 isAdmin 筛选
- `cloudfunctions/cloud-functions-admin/api/user-admin.js` - 移除 API 参数中的 isAdmin 处理
- `admin-app/src/views/Users/<USER>

### 2. 完善用户状态系统

**新增状态字段**: `status`

**状态值**:
- `active` (正常) - 用户可以正常使用所有功能
- `inactive` (屏蔽) - 云函数入口阻止调用，但可以获取用户信息
- `banned` (封禁) - 云函数入口阻止调用，小程序启动时提示并退出

**实现机制**:
1. **云函数入口状态检查** (`cloudfunctions/cloud-functions/middleware/user-status.js`)
   - 白名单接口: `getUserInfo`, `getAnnouncementList`, `getAllConfigs` 等
   - 屏蔽/封禁状态一律返回错误
   
2. **小程序端封禁处理** (`miniprogram/app.js`)
   - 启动时检查用户状态
   - 封禁状态显示提示并退出小程序

### 3. 删除用户功能

**新增 API**: `deleteUser`

**功能特性**:
- 硬删除用户及所有相关数据
- 涉及集合: `users`, `check-ins`, `vip-records`, `points-records`, `user-data`, `feedbacks`, `redemption-codes`
- 二次确认机制
- 删除统计信息

**实现文件**:
- `cloudfunctions/cloud-functions-admin/api/user-admin.js` - API 接口
- `cloudfunctions/cloud-functions-admin/db/users.js` - 数据库操作
- `admin-app/src/views/Users/<USER>

### 4. 后台管理页面调整

**新增功能**:
- ✅ 添加 `isTestUser` 字段编辑
- ✅ 添加删除用户按钮（左下角，二次确认）

**移除功能**:
- ❌ VIP 状态编辑（冗余字段，实时计算）
- ❌ 积分余额编辑（冗余字段，实时计算）

**状态显示更新**:
- `active` → "正常"
- `inactive` → "屏蔽"  
- `banned` → "封禁"

## 🔧 技术实现

### 用户状态检查中间件

```javascript
// 白名单接口
const WHITELIST_APIS = [
  'getOpenId',
  'getMiniProgramCode', 
  'getUserInfo',
  'getAnnouncementList',
  'getAllConfigs'
]

// 状态检查逻辑
async function validateUserStatus(apiType, event) {
  // 白名单接口跳过检查
  if (WHITELIST_APIS.includes(apiType)) {
    return { success: true }
  }
  
  // 检查用户状态
  const status = await checkUserStatus(openid)
  if (status === 'inactive' || status === 'banned') {
    return { success: false, message: '账号异常' }
  }
  
  return { success: true }
}
```

### 小程序端封禁处理

```javascript
async function checkUserStatus() {
  const userInfo = userManager.getUserInfo()
  if (userInfo?.status === 'banned') {
    await this.handleBannedUser()
  }
}

async function handleBannedUser() {
  await wx.showModal({
    title: '账号异常',
    content: '您的账号已被封禁，无法继续使用。',
    showCancel: false
  })
  wx.exitMiniProgram()
}
```

## 📊 数据结构变更

### 用户表 (users)

```javascript
// 新增字段
{
  status: "active",  // 用户状态
  // 移除字段: isAdmin
}

// 冗余字段说明
{
  points: 0,         // 冗余字段，实时从 points-records 计算
  vip: {             // 冗余字段，实时从 vip-records 计算
    status: false,
    expiredAt: null
  }
}
```

## 🔒 安全机制

### 1. 多层防护
- **云函数入口**: 统一状态检查，防止绕过
- **小程序端**: 主动检查封禁状态
- **白名单机制**: 确保必要接口可用

### 2. 数据完整性
- **硬删除**: 彻底清除用户相关数据
- **实时计算**: VIP 和积分状态实时更新
- **状态同步**: 前后端状态显示一致

## 🎯 使用指南

### 管理员操作

1. **设置用户状态**
   - 正常: 用户可正常使用
   - 屏蔽: 阻止云函数调用
   - 封禁: 阻止使用并强制退出

2. **删除用户**
   - 点击编辑用户 → 左下角删除按钮
   - 二次确认后执行硬删除
   - 显示删除统计信息

3. **测试用户管理**
   - 通过 `isTestUser` 字段标识
   - 用于开发和测试环境

### 开发注意事项

1. **API 调用**: 注意处理状态检查返回的错误
2. **用户信息**: 获取用户信息是白名单接口
3. **状态同步**: 用户状态变更后及时刷新界面
4. **删除操作**: 确认传递正确的用户 ID (_id)

## 📝 更新日志

### v1.1.0 (2025-08-14)
- ✅ 移除 isAdmin 字段
- ✅ 完善用户状态系统 (正常/屏蔽/封禁)
- ✅ 添加删除用户功能
- ✅ 调整后台管理页面
- ✅ 实现云函数入口状态检查
- ✅ 实现小程序端封禁状态处理
- ✅ 修复 updateUserAdmin API 注册
- ✅ 修复删除用户 ID 传递问题

## 🔍 测试建议

### 功能测试
1. **状态管理**: 测试三种状态的切换和效果
2. **删除功能**: 测试用户删除和数据清理
3. **封禁处理**: 测试小程序端封禁提示和退出
4. **权限控制**: 测试屏蔽状态下的接口访问

### 安全测试  
1. **绕过检查**: 尝试绕过状态检查机制
2. **数据完整性**: 验证删除操作的彻底性
3. **白名单**: 确认白名单接口正常工作
4. **状态同步**: 验证前后端状态一致性

## 🚀 后续优化

1. **批量操作**: 支持批量设置用户状态
2. **操作日志**: 记录管理员操作历史
3. **恢复机制**: 考虑软删除和数据恢复
4. **通知机制**: 状态变更时通知用户
