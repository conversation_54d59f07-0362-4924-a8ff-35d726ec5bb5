<template>
  <div class="users-container">
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">
          <UsersIcon :size="24" />
          用户管理
        </h1>
        <p class="page-description">统一管理系统用户信息、状态和VIP</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="refreshData">
          <template #icon>
            <RefreshCwIcon :size="16" />
          </template>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-icon total">
          <UsersIcon :size="24" />
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.total || 0 }}</div>
          <div class="stat-label">总用户数</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon active">
          <UserCheckIcon :size="24" />
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.active || 0 }}</div>
          <div class="stat-label">正常用户</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon vip">
          <CrownIcon :size="24" />
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.vip || 0 }}</div>
          <div class="stat-label">VIP用户</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon blocked">
          <UserXIcon :size="24" />
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.blocked || 0 }}</div>
          <div class="stat-label">屏蔽用户</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon banned">
          <ShieldOffIcon :size="24" />
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.banned || 0 }}</div>
          <div class="stat-label">封禁用户</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon new">
          <UserPlusIcon :size="24" />
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.todayNew || 0 }}</div>
          <div class="stat-label">今日新增</div>
        </div>
      </div>
    </div>

    <!-- 筛选和搜索 -->
    <div class="filter-section card-responsive">
      <el-form :model="filters" class="filter-form form-responsive">
        <div class="filter-grid">
          <el-form-item label="用户状态">
            <el-select v-model="filters.status" placeholder="用户状态" class="w-full">
              <el-option label="全部" value="all" />
              <el-option label="正常" value="active" />
              <el-option label="屏蔽" value="inactive" />
              <el-option label="封禁" value="banned" />
            </el-select>
          </el-form-item>

          <el-form-item label="VIP状态">
            <el-select v-model="filters.vipStatus" placeholder="VIP状态" class="w-full">
              <el-option label="全部" value="all" />
              <el-option label="VIP用户" value="true" />
              <el-option label="普通用户" value="false" />
            </el-select>
          </el-form-item>

          <el-form-item label="用户类型">
            <el-select v-model="filters.userType" placeholder="用户类型" class="w-full">
              <el-option label="全部" value="all" />
              <el-option label="正式用户" value="normal" />
              <el-option label="测试用户" value="test" />
            </el-select>
          </el-form-item>

          <el-form-item label="排序方式">
            <el-select v-model="filters.sortBy" placeholder="排序字段" class="w-full">
              <el-option label="注册时间" value="createTime" />
               <el-option label="VIP到期时间" value="vip.expiredAt" />
              <el-option label="积分数量" value="points" />
              <el-option label="用户编号" value="no" />
            </el-select>
          </el-form-item>

          <el-form-item label="排序方向">
            <el-select v-model="filters.sortOrder" class="w-full">
              <el-option label="降序" value="desc" />
              <el-option label="升序" value="asc" />
            </el-select>
          </el-form-item>
        </div>

        <div class="search-row flex gap-4 items-end">
          <el-form-item label="搜索" class="flex-1">
            <el-input
              v-model="filters.keyword"
              placeholder="搜索用户名、昵称或OpenID"
              @keyup.enter="handleSearch"
              class="mobile-w-full"
            >
              <template #prefix>
                <SearchIcon :size="16" />
              </template>
            </el-input>
          </el-form-item>

          <div class="search-actions flex gap-2">
            <el-button type="primary" @click="handleSearch" class="btn-responsive">
              <template #icon>
                <SearchIcon :size="16" />
              </template>
              搜索
            </el-button>
            <el-button @click="resetFilters" class="btn-responsive">
              <template #icon>
                <RotateCcwIcon :size="16" />
              </template>
              重置
            </el-button>
            <el-button @click="loadUserList" class="btn-responsive">
              <template #icon>
                <RefreshCwIcon :size="16" />
              </template>
              刷新
            </el-button>
          </div>
        </div>
      </el-form>
    </div>

    <!-- 用户列表 -->
    <div class="table-section">
      <el-table
        v-loading="loading"
        :data="userList"
        stripe
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="avatar" label="头像" width="80">
          <template #default="{ row }">
            <el-avatar :size="40" :src="row.avatar" :alt="row.nickname">
              <UserIcon :size="20" />
            </el-avatar>
          </template>
        </el-table-column>
        <el-table-column prop="nickname" label="昵称" min-width="150" />
        <el-table-column prop="no" label="编号" width="80" />
        <el-table-column prop="status" label="状态" width="80">
          <template #default="{ row }">
            <el-tag
              :type="getStatusType(row.status)"
              size="small"
            >
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="vip" label="VIP" width="80">
          <template #default="{ row }">
            <el-tag v-if="row.vip?.status" type="warning" size="small">
              <CrownIcon :size="12" />
              VIP
            </el-tag>
            <span v-else class="text-gray-400">普通</span>
          </template>
        </el-table-column>
        <el-table-column prop="vip.expiredAt" label="VIP到期" width="140">
          <template #default="{ row }">
            <div v-if="row.vip?.status && row.vip?.expiredAt" class="time-display">
              <div :class="getVipExpiryClass(row.vip.expiredAt)">
                {{ formatDateTime(row.vip.expiredAt) }}
              </div>
              <div class="relative-time">
                {{ getRelativeTime(row.vip.expiredAt) }}
              </div>
            </div>
            <span v-else class="text-gray-400">-</span>
          </template>
        </el-table-column>
        <el-table-column prop="isTestUser" label="类型" width="80">
          <template #default="{ row }">
            <el-tag v-if="row.isTestUser" :type="getUserTypeColor(row.isTestUser)" size="small">
              {{ getUserTypeText(row.isTestUser) }}
            </el-tag>
            <el-tag v-else :type="getUserTypeColor(row.isTestUser)" size="small">
              {{ getUserTypeText(row.isTestUser) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="注册时间" width="140">
          <template #default="{ row }">
            <div class="time-display">
              <div>{{ formatDateTime(row.createTime) }}</div>
              <div class="relative-time">{{ getRelativeTime(row.createTime) }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="lastLoginTime" label="最后登录" width="140">
          <template #default="{ row }">
            <div v-if="row.lastLoginTime" class="time-display">
              <div>{{ formatDateTime(row.lastLoginTime) }}</div>
              <div class="relative-time">{{ getRelativeTime(row.lastLoginTime) }}</div>
            </div>
            <span v-else class="text-gray-400">从未登录</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="320" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button
                type="primary"
                size="small"
                @click="viewUserDetail(row)"
              >
                查看
              </el-button>
              <el-button
                type="success"
                size="small"
                @click="editUser(row)"
              >
                编辑
              </el-button>
              <el-button
                :type="row.status === 'active' ? 'warning' : 'success'"
                size="small"
                @click="toggleUserStatus(row)"
              >
                {{ row.status === 'active' ? '屏蔽' : '恢复' }}
              </el-button>
              
              <!-- VIP 操作下拉菜单 -->
              <el-dropdown trigger="click" @command="handleVipCommand">
                <el-button type="warning" size="small">
                  VIP
                  <ChevronDownIcon :size="12" />
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item :command="{action: 'grant', user: row}" v-if="!row.vip?.status">
                      赠送VIP
                    </el-dropdown-item>
                    <el-dropdown-item :command="{action: 'renew', user: row}" v-if="row.vip?.status">
                      续期VIP
                    </el-dropdown-item>
                    <el-dropdown-item :command="{action: 'revoke', user: row}" v-if="row.vip?.status">
                      取消VIP
                    </el-dropdown-item>
                    <el-dropdown-item :command="{action: 'records', user: row}">
                      VIP记录
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>

      <!-- 批量操作 -->
      <div v-if="selectedUsers.length > 0" class="batch-actions">
        <div class="batch-info">
          已选择 {{ selectedUsers.length }} 个用户
        </div>
        <div class="batch-buttons">
          <el-button type="success" @click="batchUpdateStatus('active')">
            批量恢复
          </el-button>
          <el-button type="warning" @click="batchUpdateStatus('inactive')">
             批量屏蔽
          </el-button>
          <el-button type="danger" @click="batchUpdateStatus('banned')">
            批量封禁
          </el-button>
        </div>
      </div>
    </div>

    <!-- 用户详情对话框 -->
    <el-dialog
      v-model="userDetailDialogVisible"
      title="用户详情"
      width="800px"
      :close-on-click-modal="false"
    >
      <div v-if="currentUser" class="user-detail">
        <div class="detail-header">
          <el-avatar :size="80" :src="currentUser.avatar" :alt="currentUser.nickname">
            <UserIcon :size="40" />
          </el-avatar>
          <div class="detail-info">
            <h3>{{ currentUser.nickname }}</h3>
            <p>用户编号: {{ currentUser.no }}</p>
            <p>OpenID: {{ currentUser.openid }}</p>
          </div>
        </div>

        <div class="detail-content">
          <div class="detail-section">
            <h4>基本信息</h4>
            <div class="detail-grid">
              <div class="detail-item">
                <label>状态:</label>
                <el-tag :type="getStatusType(currentUser.status)" size="small">
                  {{ getStatusText(currentUser.status) }}
                </el-tag>
              </div>
              <div class="detail-item">
                <label>用户类型:</label>
                <el-tag v-if="currentUser.isTestUser" type="info" size="small">测试用户</el-tag>
                <span v-else>正式用户</span>
              </div>
              <div class="detail-item">
                <label>VIP状态:</label>
                <el-tag v-if="currentUser.vip?.status" type="warning" size="small">
                  <CrownIcon :size="12" />
                  VIP用户
                </el-tag>
                <span v-else>普通用户</span>
              </div>
              <div class="detail-item" v-if="currentUser.vip?.status">
                <label>VIP到期:</label>
                <span :class="getVipExpiryClass(currentUser.vip.expiredAt)">
                  {{ formatDateTime(currentUser.vip.expiredAt) }}
                </span>
              </div>
            </div>
          </div>

          <div class="detail-section">
            <h4>统计信息</h4>
            <div class="detail-grid">
              <div class="detail-item">
                <label>积分余额:</label>
                <span>{{ currentUser.points || 0 }}</span>
              </div>
              <div class="detail-item">
                <label>注册时间:</label>
                <span>{{ formatDateTime(currentUser.createTime) }}</span>
              </div>
              <div class="detail-item">
                <label>最后更新:</label>
                <span>{{ formatDateTime(currentUser.updateTime) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 用户编辑对话框 -->
    <el-dialog
      v-model="userEditDialogVisible"
      title="编辑用户"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        :model="editingUser"
        label-width="100px"
        :disabled="editLoading"
      >
        <el-form-item label="昵称" prop="nickname">
          <el-input v-model="editingUser.nickname" placeholder="请输入用户昵称" />
        </el-form-item>

        <el-form-item label="用户状态" prop="status">
          <el-select v-model="editingUser.status" placeholder="选择用户状态" style="width: 100%">
            <el-option label="正常" value="active" />
            <el-option label="屏蔽" value="inactive" />
            <el-option label="封禁" value="banned" />
          </el-select>
        </el-form-item>

        <el-form-item label="用户类型">
          <el-radio-group v-model="editingUser.isTestUser">
            <el-radio :label="false">正式用户</el-radio>
            <el-radio :label="true">测试用户</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="flex justify-between">
          <el-button
            type="danger"
            plain
            @click="deleteUser"
            :loading="editLoading"
          >
            删除用户
          </el-button>
          <div>
            <el-button @click="userEditDialogVisible = false">取消</el-button>
            <el-button type="primary" @click="saveUserEdit" :loading="editLoading">保存</el-button>
          </div>
        </div>
      </template>
    </el-dialog>

    <!-- VIP记录对话框 -->
    <el-dialog
      v-model="vipRecordsDialogVisible"
      title="VIP记录"
      width="800px"
      :destroy-on-close="true"
    >
      <div v-if="currentVipUser" class="vip-records">
        <div class="records-header">
          <h4>{{ currentVipUser.nickname }} 的VIP记录</h4>
        </div>

        <el-table :data="vipRecords" stripe>
          <el-table-column prop="type" label="类型" width="100">
            <template #default="{ row }">
              <el-tag :type="getVipRecordTypeColor(row.type)" size="small">
                {{ getVipRecordTypeText(row.type) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="duration" label="时长(天)" width="100">
            <template #default="{ row }">
              {{ row.duration || '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="reason" label="原因" min-width="150">
            <template #default="{ row }">
              {{ row.reason || '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="操作时间" width="180">
            <template #default="{ row }">
              <div v-if="row.createTime" class="time-display">
                <div>{{ formatDateTime(row.createTime) }}</div>
                <div class="relative-time">{{ getRelativeTime(row.createTime) }}</div>
              </div>
              <span v-else class="text-gray-400">-</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>

    <!-- 赠送VIP对话框 -->
    <el-dialog
      v-model="grantVipDialogVisible"
      title="赠送VIP"
      width="500px"
    >
      <el-form :model="grantVipForm" label-width="80px">
        <el-form-item label="时长(天)">
          <el-input-number
            v-model="grantVipForm.duration"
            :min="1"
            :max="3650"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="原因">
          <el-input
            v-model="grantVipForm.reason"
            placeholder="请输入赠送原因"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="grantVipDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmGrantVip">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Users as UsersIcon,
  UserCheck as UserCheckIcon,
  Crown as CrownIcon,
  UserPlus as UserPlusIcon,
  UserX as UserXIcon,
  ShieldOff as ShieldOffIcon,
  Search as SearchIcon,
  RotateCcw as RotateCcwIcon,
  RefreshCw as RefreshCwIcon,
  User as UserIcon,
  Eye as EyeIcon,
  ChevronDown as ChevronDownIcon,
  X as XIcon,
  History as HistoryIcon
} from 'lucide-vue-next'
import { callCloudFunction } from '@/api/wechat-api.js'

// 响应式数据
const loading = ref(false)
const userList = ref([])
const selectedUsers = ref([])
const currentUser = ref(null)
const userDetailDialogVisible = ref(false)
const userEditDialogVisible = ref(false)
const editingUser = ref({})
const editLoading = ref(false)

// 统计数据
const stats = ref({
  total: 0,
  active: 0,
  vip: 0,
  todayNew: 0
})

// 筛选条件
const filters = reactive({
  status: 'all',
  vipStatus: 'all',
  userType: 'all',
  sortBy: 'createTime',
  sortOrder: 'desc',
  keyword: ''
})

// 分页
const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
})

// VIP 管理相关
const grantVipDialogVisible = ref(false)
const grantVipForm = reactive({
  userId: '',
  duration: 30,
  reason: '管理员赠送'
})
const vipRecordsDialogVisible = ref(false)
const vipRecords = ref([])
const currentVipUser = ref(null)

// 方法
const refreshData = async () => {
  await Promise.all([
    loadUserList(),
    loadStats()
  ])
}

const loadUserList = async () => {
  try {
    loading.value = true

    const params = {
      status: filters.status,
      vipStatus: filters.vipStatus,
      keyword: filters.keyword,
      sortBy: filters.sortBy,
      sortOrder: filters.sortOrder,
      page: pagination.page,
      pageSize: pagination.pageSize
    }

    // 根据用户类型筛选添加 isTestUser 参数
    if (filters.userType === 'test') {
      params.isTestUser = true
    } else if (filters.userType === 'normal') {
      params.isTestUser = false
    }

    const result = await callCloudFunction('getUserList', params)

    if (result.success) {
      userList.value = result.data || []
      pagination.total = result.pagination?.total || 0
    } else {
      ElMessage.error(result.message || '获取用户列表失败')
    }
  } catch (error) {
    console.error('获取用户列表失败:', error)
    ElMessage.error('获取用户列表失败')
  } finally {
    loading.value = false
  }
}

const loadStats = async () => {
  try {
    const result = await callCloudFunction('getUserStats', { period: '30d' })
    if (result.success) {
      stats.value = result.data
    }
  } catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

const handleSearch = () => {
  pagination.page = 1
  loadUserList()
}

const resetFilters = () => {
  Object.assign(filters, {
    status: 'all',
    vipStatus: 'all',
    userType: 'all',
    sortBy: 'createTime',
    sortOrder: 'desc',
    keyword: ''
  })
  pagination.page = 1
  loadUserList()
}

const handleSelectionChange = (selection) => {
  selectedUsers.value = selection
}

const handleSizeChange = (size) => {
  pagination.pageSize = size
  pagination.page = 1
  loadUserList()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  loadUserList()
}

// 用户状态相关方法
const getStatusType = (status) => {
  const types = {
    active: 'success',
    inactive: 'warning',
    banned: 'danger'
  }
  return types[status] || 'info'
}

const getStatusText = (status) => {
  const texts = {
    active: '正常',
    inactive: '屏蔽',
    banned: '封禁'
  }
  return texts[status] || status
}

// VIP 相关方法
const getVipExpiryClass = (expiredAt) => {
  if (!expiredAt) return ''
  const now = new Date()
  const expire = new Date(expiredAt)
  const diffDays = Math.ceil((expire - now) / (1000 * 60 * 60 * 24))

  if (diffDays <= 0) return 'text-red-500'
  if (diffDays <= 7) return 'text-orange-500'
  if (diffDays <= 30) return 'text-yellow-500'
  return 'text-green-500'
}

// 格式化日期时间
const formatDateTime = (date) => {
  if (!date) return '-'
  const d = new Date(date)
  return d.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  })
}

// 获取相对时间
const getRelativeTime = (date) => {
  if (!date) return ''

  const now = new Date()
  const target = new Date(date)
  const diffMs = target - now  // 注意：target - now，正数表示未来，负数表示过去
  const absDiffMs = Math.abs(diffMs)
  const diffSeconds = Math.floor(absDiffMs / 1000)
  const diffMinutes = Math.floor(diffSeconds / 60)
  const diffHours = Math.floor(diffMinutes / 60)
  const diffDays = Math.floor(diffHours / 24)
  const diffMonths = Math.floor(diffDays / 30)
  const diffYears = Math.floor(diffDays / 365)

  // 判断是过去还是未来
  const isFuture = diffMs > 0

  if (diffSeconds < 60) {
    return isFuture ? '即将到期' : '刚刚'
  } else if (diffMinutes < 60) {
    return isFuture ? `${diffMinutes}分钟后` : `${diffMinutes}分钟前`
  } else if (diffHours < 24) {
    return isFuture ? `${diffHours}小时后` : `${diffHours}小时前`
  } else if (diffDays < 30) {
    return isFuture ? `${diffDays}天后` : `${diffDays}天前`
  } else if (diffMonths < 12) {
    return isFuture ? `${diffMonths}个月后` : `${diffMonths}个月前`
  } else {
    return isFuture ? `${diffYears}年后` : `${diffYears}年前`
  }
}

// 用户类型相关
const getUserTypeColor = (isTestUser) => {
  return isTestUser ? 'warning' : 'success'
}

const getUserTypeText = (isTestUser) => {
  return isTestUser ? '测试' : '正式'
}

// VIP记录类型相关
const getVipRecordTypeColor = (type) => {
  const colors = {
    grant: 'success',
    renew: 'primary',
    revoke: 'danger',
    expire: 'warning'
  }
  return colors[type] || 'info'
}

const getVipRecordTypeText = (type) => {
  const texts = {
    grant: '赠送',
    renew: '续期',
    revoke: '取消',
    expire: '过期'
  }
  return texts[type] || type
}

// VIP 操作处理
const handleVipCommand = (command) => {
  const { action, user } = command

  switch (action) {
    case 'grant':
      grantVip(user)
      break
    case 'renew':
      renewVip(user)
      break
    case 'revoke':
      revokeVip(user)
      break
    case 'records':
      viewVipRecords(user)
      break
  }
}

// 用户详情和编辑
const viewUserDetail = (user) => {
  currentUser.value = user
  userDetailDialogVisible.value = true
}

const editUser = (user) => {
  editingUser.value = {
    ...user,
    isTestUser: user.isTestUser || false
  }
  userEditDialogVisible.value = true
}

const saveUserEdit = async () => {
  try {
    editLoading.value = true

    const updateData = {
      userId: editingUser.value._id,
      nickname: editingUser.value.nickname,
      status: editingUser.value.status,
      isTestUser: editingUser.value.isTestUser
    }

    const result = await callCloudFunction('updateUserAdmin', updateData)

    if (result.success) {
      ElMessage.success('用户信息更新成功')
      userEditDialogVisible.value = false
      loadUserList()
      loadStats()
    } else {
      ElMessage.error(result.message || '更新用户信息失败')
    }
  } catch (error) {
    console.error('更新用户信息失败:', error)
    ElMessage.error('更新用户信息失败')
  } finally {
    editLoading.value = false
  }
}

// 用户状态切换
const toggleUserStatus = async (user) => {
  try {
    const action = user.status === 'active' ? '屏蔽' : '恢复正常'

    await ElMessageBox.confirm(
      `确定要${action}用户 "${user.nickname}" 吗？`,
      `${action}用户`,
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const newStatus = user.status === 'active' ? 'inactive' : 'active'

    const result = await callCloudFunction('updateUserAdmin', {
      userId: user._id,
      status: newStatus
    })

    if (result.success) {
      ElMessage.success(`用户${action}成功`)
      loadUserList()
      loadStats()
    } else {
      ElMessage.error(result.message || `${action}失败`)
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('切换用户状态失败:', error)
      ElMessage.error('操作失败')
    }
  }
}

// 批量操作
const batchUpdateStatus = async (status) => {
  if (selectedUsers.value.length === 0) {
    ElMessage.warning('请先选择用户')
    return
  }

  const statusText = {
    active: '恢复正常',
    inactive: '屏蔽',
    banned: '封禁'
  }[status]

  try {
    await ElMessageBox.confirm(
      `确定要${statusText} ${selectedUsers.value.length} 个用户吗？`,
      `批量${statusText}`,
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const promises = selectedUsers.value.map(user =>
      callCloudFunction('updateUserAdmin', {
        userId: user._id,
        status
      })
    )

    await Promise.all(promises)

    ElMessage.success(`批量${statusText}成功`)
    selectedUsers.value = []
    loadUserList()
    loadStats()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量操作失败:', error)
      ElMessage.error('批量操作失败')
    }
  }
}

// VIP 管理方法
const showGrantVipDialog = () => {
  Object.assign(grantVipForm, {
    userId: '',
    duration: 30,
    reason: '管理员赠送'
  })
  grantVipDialogVisible.value = true
}

const grantVip = (user) => {
  Object.assign(grantVipForm, {
    userId: user._id,
    duration: 30,
    reason: '管理员赠送'
  })
  grantVipDialogVisible.value = true
}

const renewVip = (user) => {
  Object.assign(grantVipForm, {
    userId: user._id,
    duration: 30,
    reason: 'VIP续期'
  })
  grantVipDialogVisible.value = true
}

const revokeVip = async (user) => {
  try {
    await ElMessageBox.confirm(
      `确定要取消用户 "${user.nickname}" 的VIP吗？`,
      '取消VIP',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const result = await callCloudFunction('revokeVipAdmin', {
      userId: user._id,
      reason: '管理员取消'
    })

    if (result.success) {
      ElMessage.success('VIP取消成功')
      loadUserList()
      loadStats()
    } else {
      ElMessage.error(result.message || 'VIP取消失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('取消VIP失败:', error)
      ElMessage.error('取消VIP失败')
    }
  }
}

const viewVipRecords = async (user) => {
  try {
    currentVipUser.value = user

    console.log('开始获取VIP记录，用户ID:', user._id)

    const result = await callCloudFunction('getVipRecordsAdmin', {
      userId: user._id,
      page: 1,
      pageSize: 50
    })

    console.log('VIP记录API响应:', result)

    if (result.success) {
      vipRecords.value = result.data || []
      console.log('设置VIP记录数据:', vipRecords.value)

      // 使用 nextTick 确保DOM更新后再显示对话框
      await nextTick()
      vipRecordsDialogVisible.value = true
    } else {
      ElMessage.error(result.message || '获取VIP记录失败')
    }
  } catch (error) {
    console.error('获取VIP记录失败:', error)
    ElMessage.error('获取VIP记录失败')
  }
}

// 删除用户
const deleteUser = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除用户 "${editingUser.value.nickname}" 吗？此操作将永久删除用户及其所有相关数据，无法恢复！`,
      '删除用户',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'error',
        dangerouslyUseHTMLString: true
      }
    )

    editLoading.value = true

    const result = await callCloudFunction('deleteUser', {
      userId: editingUser.value._id,
      reason: '管理员删除'
    })

    if (result.success) {
      ElMessage.success(`用户删除成功，共删除 ${result.data.totalDeleted} 条相关数据`)
      userEditDialogVisible.value = false
      loadUserList()
      loadStats()
    } else {
      ElMessage.error(result.message || '删除用户失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除用户失败:', error)
      ElMessage.error('删除用户失败')
    }
  } finally {
    editLoading.value = false
  }
}

// 确认赠送VIP
const confirmGrantVip = async () => {
  try {
    const result = await callCloudFunction('grantVipAdmin', {
      userId: grantVipForm.userId,
      duration: grantVipForm.duration,
      reason: grantVipForm.reason
    })

    if (result.success) {
      ElMessage.success('VIP赠送成功')
      grantVipDialogVisible.value = false
      loadUserList()
      loadStats()
    } else {
      ElMessage.error(result.message || 'VIP赠送失败')
    }
  } catch (error) {
    console.error('VIP赠送失败:', error)
    ElMessage.error('VIP赠送失败')
  }
}

onMounted(() => {
  refreshData()
})
</script>

<style scoped>
.users-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.page-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #333;
}

.page-description {
  margin: 8px 0 0 0;
  color: #666;
  font-size: 14px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

.stat-card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.stat-icon.total { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
.stat-icon.active { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
.stat-icon.vip { background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%); }
.stat-icon.blocked { background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%); }
.stat-icon.banned { background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%); }
.stat-icon.new { background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); }

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

.filter-section {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  margin-bottom: 20px;
}

.filter-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.search-row {
  margin-top: 16px;
}

.search-actions {
  flex-shrink: 0;
}

.table-section {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.action-buttons .el-button {
  margin: 0;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.batch-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
  padding: 12px 16px;
  background: #f0f9ff;
  border: 1px solid #0ea5e9;
  border-radius: 6px;
}

.batch-info {
  color: #0369a1;
  font-weight: 500;
}

.batch-buttons {
  display: flex;
  gap: 8px;
}

.openid-text {
  font-family: monospace;
  font-size: 12px;
  color: #666;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .users-container {
    padding: 10px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .filter-grid {
    grid-template-columns: 1fr;
  }

  .search-row {
    flex-direction: column;
    gap: 16px;
  }

  .search-actions {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
    width: 100%;
  }

  .action-buttons {
    flex-direction: column;
  }

  .batch-actions {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .batch-buttons {
    justify-content: center;
  }
}

/* VIP 到期时间颜色 */
.text-red-500 { color: #ef4444; }
.text-orange-500 { color: #f97316; }
.text-yellow-500 { color: #eab308; }
.text-green-500 { color: #22c55e; }
.text-gray-400 { color: #9ca3af; }
.text-gray-500 { color: #6b7280; }

/* 对话框样式 */
.user-detail {
  padding: 20px 0;
}

.detail-header {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #eee;
}

.detail-info h3 {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
  color: #333;
}

.detail-info p {
  margin: 4px 0;
  color: #666;
  font-size: 14px;
}

.detail-section {
  margin-bottom: 24px;
}

.detail-section h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  border-left: 4px solid #409eff;
  padding-left: 12px;
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.detail-item label {
  font-weight: 500;
  color: #666;
  min-width: 80px;
}

.vip-records {
  padding: 10px 0;
}

.records-header {
  margin-bottom: 20px;
}

.records-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

/* 工具类 */
.flex {
  display: flex;
}

.justify-between {
  justify-content: space-between;
}

.justify-center {
  justify-content: center;
}

.items-center {
  align-items: center;
}

.gap-2 {
  gap: 8px;
}

.gap-4 {
  gap: 16px;
}

.mt-1 {
  margin-top: 4px;
}

.text-sm {
  font-size: 14px;
}

/* 时间显示样式 */
.time-display {
  line-height: 1.2;
}

.time-display > div:first-child {
  font-size: 13px;
  color: #333;
  margin-bottom: 2px;
}

.relative-time {
  font-size: 11px;
  color: #999;
}

.w-full {
  width: 100%;
}
</style>
