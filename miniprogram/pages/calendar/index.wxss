/* 日历页面样式 */

page {
  /* 禁用点击高亮 */
  -webkit-tap-highlight-color: rgba(0,0,0,0);
}

/* 容器样式 */
.calendar-container {
  min-height: 100vh;
  background-color: #F5F7FB;
  display: flex;
  flex-direction: column;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
  padding-bottom: 200rpx;
}

/* 头部样式 */
.calendar-header {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  padding: 40rpx 32rpx 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  margin: 24rpx 24rpx 0;
  border-radius: 20rpx;
}

.header-title-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.header-title {
  display: flex;
  align-items: center;
}

.title-icon {
  font-size: 48rpx;
  margin-right: 12rpx;
}

.title-text {
  font-size: 40rpx;
  font-weight: bold;
  color: #2d3748;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.header-subtitle {
  font-size: 28rpx;
  color: #4a5568;
  line-height: 1.5;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

/* 当前工作履历显示 */
.current-work-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 16rpx;
  padding: 16rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12rpx;
  border: 1rpx solid rgba(0, 0, 0, 0.1);
}

.work-info-content {
  display: flex;
  align-items: center;
}

.work-status-icon {
  font-size: 24rpx;
  margin-right: 12rpx;
  font-weight: bold;
}

.work-name {
  font-size: 26rpx;
  font-weight: 500;
  color: #2d3748;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

/* 工作天数信息 */
.work-days-info {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
}

.work-days-tag {
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  border: 1rpx solid;
  font-size: 22rpx;
  font-weight: 500;
  text-shadow: none;
}

.work-switch-btn {
  padding: 10rpx 18rpx;
  background: white;
  border: 1rpx solid rgba(0, 0, 0, 0.1);
  border-radius: 12rpx;
  font-size: 24rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.work-switch-btn:active {
  background: rgba(0, 0, 0, 0.05);
  transform: scale(0.95);
}

/* 日历主体 */
.calendar-main {
  padding: 0 24rpx;
}

/* 日历主容器 */
.calendar-main {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20rpx);
  border-radius: 20rpx;
  margin: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  overflow: hidden;
}

/* 月份导航 */
.month-nav {
  padding: 24rpx 4rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.nav-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.nav-btn:active {
  background: rgba(168, 237, 234, 0.3);
  transform: scale(0.95);
}

.year-nav-icon {
  font-size: 36rpx;
  color: #2d3748;
  font-weight: bold;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.month-nav-icon {
  font-size: 36rpx;
  color: #0ea5b3;
  font-weight: bold;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.month-title-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4rpx;
}

.month-title {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.year {
  font-size: 36rpx;
  font-weight: 600;
  color: #2d3748;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.month {
  font-size: 36rpx;
  font-weight: 600;
  color: #0ea5b3;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.back-to-today-hint {
  position: fixed;
  transform: translateY(25px);
  text-align: center;
  font-size: 18rpx;
  color: #999;
  cursor: pointer;
  transition: color 0.3s ease;
}

/* 星期标题 */
.weekdays {
  padding: 20rpx 16rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  display: flex;
}

.weekday {
  flex: 1;
  text-align: center;
  font-size: 28rpx;
  font-weight: 600;
  color: #4a5568;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

/* 日历网格 */
.calendar-grid {
  padding: 16rpx 20rpx 20rpx;
  display: flex;
  flex-wrap: wrap;
}

.day-cell {
  width: calc(100% / 7);
  aspect-ratio: 1 / 1;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  transition: all 0.3s ease;
  box-sizing: border-box;
  border: 2rpx solid rgb(0, 0, 0, 0%);
  padding: 4rpx;
}

.day-empty {
  pointer-events: none;
}

.day-active {
  cursor: pointer;
}

.day-number {
  font-size: 32rpx;
  font-weight: 500;
  color: #646378;
  z-index: 2;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

/* 今天样式 */
.day-today {
  background: linear-gradient(135deg, #D1FAE5 0%, #A7F3D0 100%);
  border: 2rpx solid #10B981;
}

.day-today .day-number {
  color: #059669;
  font-weight: bold;
}

.today-indicator {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  font-size: 16rpx;
  color: white;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 6rpx;
  padding: 2rpx 6rpx;
}

/* 发薪日标识 */
.payday-indicator {
  position: absolute;
  bottom: 8rpx;
  right: 8rpx;
  font-size: 20rpx;
  z-index: 10;
}

/* 选中日期样式 */
.day-selected {
  background: linear-gradient(135deg, #e9f6ff 0%, #d6f4ff 100%) !important;
  border: 2rpx solid #bec1ff;
}

.day-selected .day-number {
  color: #1897ff;
  font-weight: bold;
}

/* 有数据的日期样式 - 移除，改用状态背景色 */
.day-has-data {
  /* 样式由状态类型决定 */
}

/* 数据指示点样式已移除 */

/* 状态指示器 */
.status-indicator {
  position: absolute;
  top: 4rpx;
  left: 4rpx;
  z-index: 3;
}

.status-icon {
  font-size: 16rpx;
}

/* 节假日指示器 */
.holiday-indicator {
  position: absolute;
  bottom: 4rpx;
  left: 50%;
  transform: translateX(-50%);
  z-index: 3;
  max-width: 80rpx;
  overflow: hidden;
}

.holiday-text {
  font-size: 16rpx;
  color: #dc2626;
  font-weight: 600;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  text-align: center;
  display: block;
}

/* 日期状态样式 - 使用动态配置 */
/* 工作/出勤状态类 - 蓝色系 */
.day-status-work,
.day-status-rest,
.day-status-rotation_rest,
.day-status-compensatory_rest,
.day-status-work_suspension,
.day-status-standby,
.day-status-duty {
  background: var(--status-bg-color, #EFF6FF);
}

.day-status-work .day-number,
.day-status-rest .day-number,
.day-status-rotation_rest .day-number,
.day-status-compensatory_rest .day-number,
.day-status-work_suspension .day-number,
.day-status-standby .day-number,
.day-status-duty .day-number {
  color: var(--status-color, #3B82F6);
}

/* 法定/特殊假日类 - 绿色系 */
.day-status-holiday,
.day-status-weekend,
.day-status-adjusted_leave,
.day-status-annual_leave,
.day-status-festival_leave {
  background: var(--status-bg-color, #F0FDF4);
}

.day-status-holiday .day-number,
.day-status-weekend .day-number,
.day-status-adjusted_leave .day-number,
.day-status-annual_leave .day-number,
.day-status-festival_leave .day-number {
  color: var(--status-color, #10B981);
}

/* 请假/缺勤类 - 橙色系 */
.day-status-leave,
.day-status-sick,
.day-status-marriage_leave,
.day-status-maternity_leave,
.day-status-paternity_leave,
.day-status-bereavement_leave,
.day-status-work_injury_leave,
.day-status-family_visit_leave,
.day-status-absent {
  background: var(--status-bg-color, #FFFBEB);
}

.day-status-leave .day-number,
.day-status-sick .day-number,
.day-status-marriage_leave .day-number,
.day-status-maternity_leave .day-number,
.day-status-paternity_leave .day-number,
.day-status-bereavement_leave .day-number,
.day-status-work_injury_leave .day-number,
.day-status-family_visit_leave .day-number,
.day-status-absent .day-number {
  color: var(--status-color, #F59E0B);
}

/* 确保今天和选中状态的优先级 */
.day-today.day-selected {
  background: linear-gradient(135deg, #D1FAE5 0%, #A7F3D0 100%);
  border: 2rpx solid #10B981;
}

.day-today.day-selected .day-number {
  color: #059669;
  font-weight: bold;
}

/* 不在任职日期范围内的日期样式 */
.day-outside-employment .day-number {
  color: #9ca3af !important; /* 灰色文字 */
  opacity: 0.6;
}

/* 确保不在任职范围内的日期在各种状态下都保持灰色 */
.day-outside-employment.day-today .day-number,
.day-outside-employment.day-selected .day-number,
.day-outside-employment.day-has-data .day-number {
  color: #9ca3af !important;
  opacity: 0.6;
}

/* 不在任职范围内的日期的状态样式也要调整 */
.day-outside-employment.day-status-work .day-number,
.day-outside-employment.day-status-leave .day-number,
.day-outside-employment.day-status-holiday .day-number,
.day-outside-employment.day-status-sick .day-number,
.day-outside-employment.day-status-rest .day-number {
  color: #9ca3af !important;
  opacity: 0.6;
}

/* 节假日样式中的不在任职范围内的日期 */
.day-outside-employment.holiday-type-holiday .day-number,
.day-outside-employment.holiday-type-workday .day-number,
.day-outside-employment.holiday-type-weekend .day-number {
  color: #9ca3af !important;
  opacity: 0.6;
}

/* 节假日样式 - 移除背景色和边框 */
.day-cell.holiday-type-holiday {
  /* 移除背景色和边框 */
}

.day-cell.holiday-type-holiday .day-number {
  color: #dc2626;
  font-weight: 600;
}

.day-cell.holiday-type-workday {
  /* 移除背景色和边框 */
}

.day-cell.holiday-type-workday .day-number {
  color: #3b82f6;
  font-weight: 600;
}

/* 通用卡片样式 */
.date-info-card,
.fishing-records-card,
.income-adjustment-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20rpx);
  border-radius: 20rpx;
  padding: 32rpx;
  margin: 0 24rpx 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

/* 卡片顶部日期信息样式 */
.card-date-info {
  text-align: center;
}

.card-date-title {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12rpx;
}

.card-date-title .date-text {
  font-size: 32rpx;
  color: #2d3748;
  font-weight: 600;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.day-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.day-title {
  display: flex;
  align-items: center;
}

.section-title-text {
  font-size: 28rpx;
  color: #2d3748;
  font-weight: 600;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.date-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.date-text {
  font-size: 32rpx;
  color: #2d3748;
  font-weight: 600;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.day-income {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.income-label {
  font-size: 24rpx;
  color: #4a5568;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.income-value {
  font-size: 28rpx;
  color: #10B981;
  font-weight: bold;
}

/* 节假日信息显示 */
.holiday-info-display {
  margin: 16rpx 0;
}

.holiday-badge {
  display: inline-flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16rpx;
  padding: 8rpx 16rpx;
  gap: 8rpx;
  backdrop-filter: blur(8rpx);
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid rgba(0, 0, 0, 0.1);
}

.holiday-name {
  font-size: 24rpx;
  font-weight: 600;
  color: #dc2626;
}

.holiday-work-status {
  font-size: 20rpx;
  padding: 2rpx 8rpx;
  border-radius: 8rpx;
  background: rgba(220, 38, 38, 0.1);
  color: #dc2626;
}

.holiday-badge.holiday-holiday .holiday-name {
  color: #dc2626;
}

.holiday-badge.holiday-holiday .holiday-work-status {
  background: rgba(220, 38, 38, 0.1);
  color: #dc2626;
}

.holiday-badge.holiday-workday .holiday-name {
  color: #3b82f6;
}

.holiday-badge.holiday-workday .holiday-work-status {
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
}

.holiday-badge.holiday-weekend .holiday-name {
  color: #6b7280;
}

.holiday-badge.holiday-weekend .holiday-work-status {
  background: rgba(107, 114, 128, 0.1);
  color: #6b7280;
}

/* 图表区域样式 */
.chart-section {
  margin-bottom: 24rpx;
}

/* 时间统计区域样式 */
.time-stats-section {
  margin-bottom: 24rpx;
}

.time-stats-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-top: 16rpx;
}

.time-stat-item {
  flex: 1;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20rpx);
  border-radius: 16rpx;
  padding: 24rpx 0 24rpx 0;
  text-align: center;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.time-stat-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6rpx;
  background: linear-gradient(90deg, #e2e8f0, #cbd5e1);
}

.work-stat::before {
  background: linear-gradient(90deg, #dbeafe, #3b82f6);
}

.rest-stat::before {
  background: linear-gradient(90deg, #d1fae5, #10b981);
}

.overtime-stat::before {
  background: linear-gradient(90deg, #fee2e2, #ef4444);
}

.fishing-count-stat::before {
  background: linear-gradient(90deg, #d1fae5, #10b981);
}

.fishing-duration-stat::before {
  background: linear-gradient(90deg, #d1fae5, #10b981);
}

.fishing-income-stat::before {
  background: linear-gradient(90deg, #d1fae5, #10b981);
}

.income-stat::before {
  background: linear-gradient(90deg, #d1fae5, #10b981);
}

.extra-income-stat::before {
  background: linear-gradient(90deg, #d1fae5, #10b981);
}

.deduction-stat::before {
  background: linear-gradient(90deg, #fee2e2, #ef4444);
}

.net-stat::before {
  background: linear-gradient(90deg, #fef3c7, #f59e0b);
}

.stat-icon {
  font-size: 40rpx;
  margin-bottom: 12rpx;
  opacity: 0.8;
  display: block;
}

.stat-info {
  flex: 1;
  width: 100%;

  display: flex;
  flex-direction: column;
  place-content: space-evenly;
}

.stat-label {
  font-size: 20rpx;
  color: #64748b;
  margin-bottom: 6rpx;
  display: block;
}

.stat-value {
  font-size: 20rpx;
  font-weight: 600;
  color: #1e293b;
  display: block;
}

.stat-sub-value {
  font-size: 22rpx;
  font-weight: 600;
  color: #10b981;
  margin-top: 6rpx;
  display: block;
}

/* 时间段列表 */
.segments-section {
  margin-bottom: 0;
}

.section-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 28rpx;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 16rpx;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.section-title-left {
  display: flex;
  align-items: center;
}

.section-title-simple {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 16rpx;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.section-icon {
  font-size: 24rpx;
  margin-right: 16rpx;
}

.empty-segments {
  text-align: center;
  padding: 40rpx 20rpx;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 16rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #4a5568;
  margin-bottom: 8rpx;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.empty-tip {
  font-size: 24rpx;
  color: #718096;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.segments-list {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.segment-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  position: relative;
  border-radius: 16rpx;
  margin-bottom: 12rpx;
  border: none;
}

.segment-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

/* 不同类型的背景色 */
.segment-work {
  background: linear-gradient(135deg, rgba(219, 234, 254, 0.8) 0%, rgba(248, 250, 252, 0.9) 100%);
  border-left: 8rpx solid #3b82f6;
}

.segment-rest {
  background: linear-gradient(135deg, rgba(209, 250, 229, 0.8) 0%, rgba(248, 250, 252, 0.9) 100%);
  border-left: 8rpx solid #10b981;
}

.segment-overtime {
  background: linear-gradient(135deg, rgba(254, 226, 226, 0.8) 0%, rgba(248, 250, 252, 0.9) 100%);
  border-left: 8rpx solid #ef4444;
}

.segment-left {
  display: flex;
  align-items: center;
  gap: 20rpx;
  flex: 1;
}

.segment-right {
  text-align: right;
}

.segment-time {
  flex: 1;
}

.time-text {
  font-size: 28rpx;
  color: #2d3748;
  font-weight: 500;
  margin-bottom: 4rpx;
  display: block;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.duration-text {
  font-size: 20rpx;
  color: #4a5568;
  background: rgba(168, 237, 234, 0.2);
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
  display: inline-block;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.type-badge {
  padding: 8rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  color: white;
  width: 20rpx;
}

.type-work {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.type-rest {
  background: linear-gradient(135deg, #10b981, #047857);
}

.type-overtime {
  background: linear-gradient(135deg, #ef4444, #dc2626);
}

.segment-income {
  text-align: center;
}

.income-text {
  font-size: 24rpx;
  color: #10B981;
  font-weight: 600;
  display: block;
}

.rate-text {
  font-size: 20rpx;
  color: #4a5568;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

/* 无偿标识样式 */
.unpaid-badge {
  display: inline-block;
  padding: 4rpx 12rpx;
  background: linear-gradient(135deg, #f97316, #ea580c);
  color: white;
  font-size: 20rpx;
  font-weight: 600;
  border-radius: 12rpx;
  text-align: center;
  box-shadow: 0 2rpx 4rpx rgba(249, 115, 22, 0.3);
  letter-spacing: 1rpx;
}

.segment-unpaid {
  text-align: center;
}

.fishing-unpaid {
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 操作按钮 */
.day-actions {
  display: flex;
  gap: 16rpx;
}

/* 操作按钮卡片样式 */
.actions-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20rpx);
  border-radius: 20rpx;
  margin: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.actions-content {
  padding: 24rpx;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.danger-section {
  margin-top: 8rpx;
  padding-top: 24rpx;
  border-top: 1rpx solid rgba(239, 68, 68, 0.2);
}

.action-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.action-btn.primary {
  background: linear-gradient(135deg, #06b6d4 0%, #10b981 100%);
  color: white;
  box-shadow: 0 8rpx 24rpx rgba(6, 182, 212, 0.3);
}

.action-btn.primary:active {
  transform: translateY(2rpx) scale(0.98);
  box-shadow: 0 4rpx 16rpx rgba(6, 182, 212, 0.3);
}

/* 皇冠图标样式 */
.crown-icon {
  width: 32rpx;
  height: 32rpx;
  margin-left: 16rpx;
}

.action-btn.secondary {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10rpx);
  color: #2d3748;
  border: 1rpx solid rgba(255, 255, 255, 0.5);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.action-btn.secondary:active {
  background: rgba(255, 255, 255, 0.9);
  transform: translateY(2rpx) scale(0.98);
}

.action-btn.danger {
  background: rgba(239, 68, 68, 0.1);
  color: #dc2626;
  border: 1rpx solid rgba(239, 68, 68, 0.3);
  box-shadow: 0 4rpx 16rpx rgba(239, 68, 68, 0.15);
}

.action-btn.danger:active {
  background: rgba(239, 68, 68, 0.2);
  transform: translateY(2rpx) scale(0.98);
}

/* 编辑计划按钮样式 */
.schedule-actions {
  margin-top: 24rpx;
  padding-top: 24rpx;
  border-top: 1rpx solid rgba(0, 0, 0, 0.1);
}

.schedule-actions .action-btn {
  width: auto;
  margin: 0;
}

/* 危险操作区域样式 */
.danger-section .action-btn.danger {
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
  color: white;
  box-shadow: 0 8rpx 24rpx rgba(220, 38, 38, 0.4);
  font-weight: 600;
}

.danger-section .action-btn.danger:active {
  background: linear-gradient(135deg, #b91c1c 0%, #991b1b 100%);
  transform: translateY(2rpx) scale(0.98);
  box-shadow: 0 4rpx 16rpx rgba(220, 38, 38, 0.4);
}

.danger-tip {
  display: flex;
  align-items: flex-start;
  gap: 8rpx;
  margin-top: 20rpx;
  padding: 16rpx;
  background: rgba(254, 226, 226, 0.8);
  border-radius: 12rpx;
  border: 1rpx solid rgba(239, 68, 68, 0.2);
}

.tip-icon {
  font-size: 24rpx;
  color: #dc2626;
  margin-top: 2rpx;
}

.tip-text {
  font-size: 22rpx;
  color: #7f1d1d;
  line-height: 1.5;
}

/* 摸鱼记录列表 */
.fishing-section {
  margin-top: 32rpx;
}

.fishing-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  margin-top: 24rpx;
}

.fishing-item {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  background: linear-gradient(135deg, rgba(254, 243, 199, 0.8) 0%, rgba(248, 250, 252, 0.9) 100%);
  border-left: 8rpx solid #fbbf24;
  border-radius: 16rpx;
  margin-bottom: 12rpx;
  position: relative;
}

.fishing-item:last-child {
  margin-bottom: 0;
}

.fishing-left {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.type-fishing {
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
}

.type-income {
  background: linear-gradient(135deg, #10b981, #059669);
}

.type-deduction {
  background: linear-gradient(135deg, #ef4444, #dc2626);
}

.fishing-content {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
  flex: 1;
}

/* 摸鱼记录中的时长标签使用黄色主题 */
.fishing-item .duration-text {
  background: rgba(251, 191, 36, 0.2);
}

/* 摸鱼记录右箭头 */
.fishing-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 20rpx;
}

.fishing-arrow .arrow-icon {
  font-size: 32rpx;
  color: #9ca3af;
  font-weight: 300;
}

.fishing-title {
  display: flex;
  flex-direction: column;
  gap: 2rpx;
}

.fishing-subtitle {
  margin-top: 4rpx;
}

.fishing-center {
  display: flex;
  align-items: center;
}

.remark-text {
  font-size: 22rpx;
  color: #6b7280;
  word-break: break-all;
  line-height: 1.4;
}

.fishing-right {
  display: flex;
  align-items: center;
}

.fishing-value {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4rpx;
}

.fishing-value .value-text {
  font-size: 24rpx;
  color: #10b981;
  font-weight: 600;
}

.fishing-value .rate-text {
  font-size: 20rpx;
  color: #6b7280;
}

.fishing-actions {
  display: flex;
  gap: 8rpx;
}

.action-btn-small {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 60rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  transition: all 0.3s ease;
}

.action-btn-small.edit {
  background: #e0f2fe;
  color: #0369a1;
}

.action-btn-small.edit:active {
  background: #bae6fd;
  transform: scale(0.95);
}

.action-btn-small.delete {
  background: #fef2f2;
  color: #dc2626;
}

.action-btn-small.delete:active {
  background: #fee2e2;
  transform: scale(0.95);
}

/* 添加摸鱼按钮 */
.add-fishing-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 16rpx;
  background: #1890ff;
  border-radius: 20rpx;
  color: #fff;
  font-size: 22rpx;
  transition: all 0.3s ease;
}

.add-fishing-btn:active {
  background: #096dd9;
  transform: scale(0.95);
}

.add-icon {
  font-size: 24rpx;
  font-weight: 600;
}

.add-text {
  font-size: 22rpx;
  font-weight: 500;
}

/* 空状态 */
.empty-fishing {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 32rpx;
  text-align: center;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 16rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.empty-tip {
  font-size: 24rpx;
  color: #999;
}

/* 摸鱼价值信息 */
.fishing-value {
  display: flex;
  gap: 8rpx;
  align-items: center;
  margin: 8rpx 0;
}

.value-item {
  display: flex;
  align-items: center;
  gap: 4rpx;
}

.value-label {
  font-size: 22rpx;
  color: #666;
  font-weight: 500;
}

.value-text {
  font-size: 22rpx;
  color: #333;
  font-weight: 600;
}

.btn-icon {
  margin-right: 12rpx;
  font-size: 28rpx;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .month-title {
    flex-direction: column;
    gap: 4rpx;
  }

  .year, .month {
    font-size: 32rpx;
  }

  .day-cell {
    height: 100rpx;
  }

  .day-number {
    font-size: 28rpx;
  }
} 

/* 引导界面样式 */
.no-work-guide {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

.guide-content {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 32rpx;
  padding: 60rpx 40rpx;
  text-align: center;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.1);
  max-width: 600rpx;
  width: 100%;
}

.guide-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
}

.guide-title {
  font-size: 48rpx;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 24rpx;
  letter-spacing: 0.5rpx;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.guide-text {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
  font-size: 28rpx;
  color: #4a5568;
  line-height: 1.6;
  margin-bottom: 48rpx;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.guide-btn {
  background: linear-gradient(135deg, #06b6d4 0%, #10b981 100%);
  color: white;
  border: none;
  border-radius: 24rpx;
  padding: 24rpx 48rpx;
  font-size: 32rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  box-shadow: 0 8rpx 32rpx rgba(6, 182, 212, 0.4);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.guide-btn:active {
  transform: scale(0.96);
  box-shadow: 0 6rpx 24rpx rgba(6, 182, 212, 0.5);
}

.btn-icon {
  margin-right: 12rpx;
  font-size: 24rpx;
}

/* 收入调整部分样式 */

.adjustment-actions {
  display: flex;
  gap: 16rpx;
}

.add-adjustment-btn {
  display: flex;
  align-items: center;
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  transition: all 0.2s ease;
  cursor: pointer;
}

.add-adjustment-btn.income {
  background-color: #e8f5e8;
  color: #2e7d32;
  border: 1rpx solid #c8e6c9;
}

.add-adjustment-btn.income:active {
  background-color: #c8e6c9;
}

.add-adjustment-btn.deduction {
  background-color: #ffebee;
  color: #c62828;
  border: 1rpx solid #ffcdd2;
}

.add-adjustment-btn.deduction:active {
  background-color: #ffcdd2;
}

.add-adjustment-btn.fishing {
  background-color: #fef3c7;
  color: #d97706;
  border: 1rpx solid #fbbf24;
}

.add-adjustment-btn.fishing:active {
  background-color: #fbbf24;
  color: #fff;
}

.add-adjustment-btn.edit-plan {
  background-color: #e3f2fd;
  color: #1976d2;
  border: 1rpx solid #bbdefb;
}

.add-adjustment-btn.edit-plan:active {
  background-color: #bbdefb;
}

.add-adjustment-btn .add-icon {
  font-size: 20rpx;
  margin-right: 8rpx;
}

.add-adjustment-btn .add-text {
  font-weight: 500;
}

/* 收入调整列表 */
.adjustment-list {
  margin-top: 24rpx;
}

.adjustment-category-title {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.category-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

/* 瓷砖式布局 */
.adjustment-tile {
  display: flex;
  align-items: stretch;
  border-radius: 16rpx;
  margin-bottom: 16rpx;
  overflow: hidden;
  position: relative;
  min-height: 120rpx;
  border-left: 8rpx solid;
}

.adjustment-tile:last-child {
  margin-bottom: 0;
}

.income-tile {
  background: linear-gradient(135deg, rgba(209, 250, 229, 0.8) 0%, rgba(248, 250, 252, 0.9) 100%);
  border-left-color: #10b981;
}

.deduction-tile {
  background: linear-gradient(135deg, rgba(254, 226, 226, 0.8) 0%, rgba(248, 250, 252, 0.9) 100%);
  border-left-color: #ef4444;
}

/* 左侧指示器移除，使用边框代替 */
.tile-indicator {
  display: none;
}

/* 瓷砖内容区域 */
.tile-content {
  flex: 1;
  padding: 24rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.tile-left {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 6rpx;
}

.tile-type {
  font-size: 26rpx;
  font-weight: 600;
  color: #374151;
}

.tile-desc {
  font-size: 22rpx;
  color: #6b7280;
  line-height: 1.4;
}

.tile-right {
  display: flex;
  align-items: center;
}

.tile-amount {
  font-size: 30rpx;
  font-weight: 700;
  letter-spacing: 0.5rpx;
}

.income-text {
  color: #10b981;
}

.deduction-text {
  color: #ef4444;
}

.adjustment-type {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
}

/* 右箭头 */
.tile-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 24rpx;
}

.arrow-icon {
  font-size: 32rpx;
  color: #9ca3af;
  font-weight: 300;
}



/* 空状态 */
.empty-adjustments {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 32rpx;
  text-align: center;
}

.empty-adjustments .empty-icon {
  font-size: 80rpx;
  margin-bottom: 24rpx;
  opacity: 0.6;
}

.empty-adjustments .empty-text {
  font-size: 32rpx;
  color: #666666;
  margin-bottom: 12rpx;
  font-weight: 500;
}

.empty-adjustments .empty-tip {
  font-size: 26rpx;
  color: #999999;
  line-height: 1.5;
}