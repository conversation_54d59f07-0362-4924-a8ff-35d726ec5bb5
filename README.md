# 摸鱼记账小程序

一个功能完整的记账小程序，包含用户端小程序、云函数后端和跨平台后台管理系统。

## 📋 项目概述

摸鱼记账是一个基于微信小程序的记账应用，提供完整的记账功能、积分系统、商店购买、签到打卡等功能，同时配备了功能强大的后台管理系统。

### 🎯 主要功能

#### 📱 小程序端（用户端）
- **💰 记账功能**：收入支出记录、分类管理、统计分析
- **🎯 积分系统**：签到获取积分、积分排行榜、积分兑换
- **🛒 积分商店**：商品购买、库存管理、订单记录
- **✅ 签到打卡**：每日签到、连续签到奖励
- **💬 意见反馈**：用户反馈、问题报告
- **📢 系统公告**：重要通知、活动公告
- **🔗 友情应用**：应用推荐、跳转链接
- **🎣 摸鱼状态**：工作状态记录、摸鱼统计
- **👑 VIP 功能**：会员特权、高级功能

#### 🖥️ 后台管理系统
- **📊 数据仪表板**：实时统计、数据可视化
- **👥 用户管理**：用户信息、VIP管理、行为分析
- **💰 积分管理**：积分记录、规则配置、排行榜
- **🛒 商店管理**：商品管理、库存控制、销售统计
- **✅ 签到管理**：签到记录、统计分析
- **💬 反馈管理**：用户反馈处理、回复管理
- **📢 公告管理**：公告发布、统计分析
- **🔗 友情应用管理**：链接管理、点击统计
- **🎣 摸鱼状态管理**：状态记录、数据分析
- **⚙️ 系统设置**：参数配置、权限管理

### 🏗️ 技术架构

```
┌─────────────────┐    ┌─────────────────┐
│   小程序前端     │    │  后台管理应用    │
│   (miniprogram) │    │   (admin-app)   │
└─────────┬───────┘    └─────────┬───────┘
          │                      │
          ▼                      ▼
┌─────────────────┐    ┌─────────────────┐
│   用户端云函数   │    │  管理端云函数    │
│(cloud-functions)│    │(cloud-functions-│
│                 │    │     admin)      │
└─────────┬───────┘    └─────────┬───────┘
          │                      │
          └──────────┬───────────┘
                     ▼
          ┌─────────────────┐
          │  微信云开发数据库 │
          │   + 云存储      │
          └─────────────────┘
```

## 🚀 快速开始

### 环境要求

- **Node.js**: >= 18.0.0
- **微信开发者工具**: 最新版本
- **Rust**: >= 1.70.0 (后台管理应用)
- **pnpm**: >= 8.0.0 (推荐)

### 1. 克隆项目

```bash
git clone https://github.com/your-username/mp-md.git
cd mp-md
```

### 2. 小程序开发

```bash
# 1. 使用微信开发者工具打开 miniprogram 目录
# 2. 配置 AppID 和云环境
# 3. 部署云函数

cd cloudfunctions/cloud-functions
npm install
npm run deploy
```

### 3. 后台管理应用开发

```bash
cd admin-app

# 安装依赖
pnpm install

# 开发模式
pnpm tauri dev

# 构建应用
pnpm tauri build
```

### 4. 管理端云函数部署

```bash
cd cloudfunctions/cloud-functions-admin
npm install
npm run deploy
```

## 📁 项目结构

```
mp-md/
├── miniprogram/                      # 小程序前端
│   ├── pages/                        # 页面文件
│   │   ├── index/                    # 首页
│   │   ├── profile/                  # 个人中心
│   │   ├── points/                   # 积分页面
│   │   ├── check-in/                 # 签到页面
│   │   ├── store/                    # 商店页面
│   │   ├── feedback/                 # 反馈页面
│   │   ├── announcements/            # 公告页面
│   │   └── ...                       # 其他页面
│   ├── components/                   # 组件文件
│   ├── utils/                        # 工具函数
│   ├── app.js                        # 小程序入口
│   └── app.json                      # 小程序配置
├── cloudfunctions/                   # 云函数
│   ├── cloud-functions/              # 用户端云函数
│   │   ├── index.js                  # 云函数入口
│   │   ├── api/                      # API 模块
│   │   │   ├── user.js               # 用户相关 API
│   │   │   ├── points.js             # 积分相关 API
│   │   │   ├── check-in.js           # 签到相关 API
│   │   │   ├── store.js              # 商店相关 API
│   │   │   ├── feedback.js           # 反馈相关 API
│   │   │   └── ...                   # 其他 API
│   │   ├── middleware/               # 中间件
│   │   └── utils/                    # 工具函数
│   └── cloud-functions-admin/        # 管理端云函数
│       ├── index.js                  # 云函数入口
│       ├── api/                      # 管理 API
│       │   ├── user-admin.js         # 用户管理 API
│       │   ├── points-admin.js       # 积分管理 API
│       │   ├── system-admin.js       # 系统管理 API
│       │   ├── feedback-admin.js     # 反馈管理 API
│       │   └── ...                   # 其他管理 API
│       ├── middleware/               # 权限验证
│       └── utils/                    # 工具函数
├── admin-app/                        # 后台管理应用
│   ├── src/                          # 前端源码
│   │   ├── views/                    # 页面组件
│   │   │   ├── Dashboard/            # 仪表板
│   │   │   ├── Users/                # 用户管理
│   │   │   ├── Points/               # 积分管理
│   │   │   ├── CheckIn/              # 签到管理
│   │   │   ├── Shop/                 # 商店管理
│   │   │   ├── Feedback/             # 反馈管理
│   │   │   ├── Announcements/        # 公告管理
│   │   │   ├── FriendApps/           # 友情应用
│   │   │   ├── FishingStatus/        # 摸鱼状态
│   │   │   ├── VIP/                  # VIP管理
│   │   │   ├── Settings/             # 系统设置
│   │   │   └── Setup/                # 初始配置
│   │   ├── components/               # 公共组件
│   │   ├── api/                      # API 接口
│   │   │   ├── wechat-api.js         # 微信云开发 API
│   │   │   ├── tauri-api.js          # Tauri 原生 API
│   │   │   └── dashboard.js          # 仪表板 API
│   │   ├── router/                   # 路由配置
│   │   ├── stores/                   # 状态管理
│   │   └── utils/                    # 工具函数
│   ├── src-tauri/                    # Tauri 后端
│   │   ├── src/                      # Rust 源码
│   │   │   ├── lib.rs                # 主模块
│   │   │   └── http_client.rs        # HTTP 客户端
│   │   ├── capabilities/             # 权限配置
│   │   ├── gen/                      # 生成的文件
│   │   └── Cargo.toml                # Rust 依赖
│   ├── public/                       # 静态资源
│   ├── dist/                         # 构建输出
│   └── README.md                     # 后台应用文档
├── docs/                             # 项目文档
├── DEVELOPMENT_GUIDE.md              # 开发规范
└── README.md                         # 项目说明
```

## 🔧 配置说明

### 1. 微信小程序配置

在 `miniprogram/app.js` 中配置云环境：

```javascript
wx.cloud.init({
  env: 'your-cloud-env-id', // 云环境 ID
  traceUser: true
})
```

### 2. 云函数环境变量

在云函数中配置以下环境变量：

```bash
# 管理端云函数环境变量
ADMIN_SECRET_KEYS=MDMPADMINKEY,YOUR_CUSTOM_KEY
```

### 3. 后台管理应用配置

首次使用需要在 Setup 页面配置：

- **AppID**: 小程序 AppID
- **AppSecret**: 小程序密钥
- **云环境ID**: 微信云开发环境 ID
- **云函数名称**: 管理端云函数名
- **管理密钥**: 管理端访问密钥

## 📱 功能模块

### 用户端功能

| 模块 | 功能描述 | 状态 |
|------|----------|------|
| 记账 | 收支记录、分类管理、统计分析 | ✅ |
| 积分 | 签到获取、积分兑换、排行榜 | ✅ |
| 商店 | 商品购买、订单管理 | ✅ |
| 签到 | 每日签到、连续奖励 | ✅ |
| 反馈 | 意见反馈、问题报告 | ✅ |
| 公告 | 系统通知、活动公告 | ✅ |
| VIP | 会员特权、高级功能 | ✅ |

### 管理端功能

| 模块 | 功能描述 | 状态 |
|------|----------|------|
| 仪表板 | 数据统计、可视化图表 | ✅ |
| 用户管理 | 用户信息、VIP管理 | ✅ |
| 积分管理 | 积分记录、规则配置 | ✅ |
| 商店管理 | 商品管理、库存控制 | ✅ |
| 反馈管理 | 反馈处理、回复管理 | ✅ |
| 公告管理 | 公告发布、统计分析 | ✅ |
| 系统设置 | 参数配置、权限管理 | ✅ |

## 🔌 API 接口

### 用户端 API

```javascript
// 获取用户信息
await wx.cloud.callFunction({
  name: 'cloud-functions',
  data: {
    type: 'getUserInfo',
    version: '1.0.0'
  }
})

// 获取积分余额
await wx.cloud.callFunction({
  name: 'cloud-functions',
  data: {
    type: 'getPointsBalance',
    version: '1.0.0'
  }
})

// 执行签到
await wx.cloud.callFunction({
  name: 'cloud-functions',
  data: {
    type: 'checkIn',
    version: '1.0.0'
  }
})

// 购买商品
await wx.cloud.callFunction({
  name: 'cloud-functions',
  data: {
    type: 'purchaseItem',
    data: {
      itemId: 'item123',
      quantity: 1
    },
    version: '1.0.0'
  }
})
```

### 管理端 API

```javascript
// 获取用户列表
await callCloudFunction('getUserList', {
  page: 1,
  limit: 20,
  keyword: ''
})

// 获取系统统计
await callCloudFunction('getSystemStats', {})

// 获取仪表板数据
await callCloudFunction('getDashboardStats', {})

// 更新用户状态
await callCloudFunction('updateUserStatus', {
  userId: 'user123',
  isVip: true,
  vipExpireAt: '2024-12-31'
})
```

## 🧪 测试

### 小程序测试

```bash
# 在微信开发者工具中
# 1. 真机预览测试
# 2. 云函数测试
# 3. 性能分析
```

### 后台管理测试

```bash
cd admin-app

# 单元测试
pnpm test

# 端到端测试
pnpm test:e2e

# 构建测试
pnpm tauri build
```

## 📚 文档

- [开发规范与流程](./DEVELOPMENT_GUIDE.md)
- [后台管理应用文档](./admin-app/README.md)
- [API 接口文档](./docs/API.md)
- [部署指南](./docs/DEPLOYMENT.md)
- [用户系统完善文档](./USER_SYSTEM_ENHANCEMENT.md)
- [用户ID统一修复文档](./USER_ID_UNIFICATION_FIX.md)
- [VIP管理页面合并文档](./VIP_MANAGEMENT_MERGE.md)

## 🚨 常见问题

### 1. 云函数部署失败

**解决方案**：
- 检查网络连接
- 确认云环境配置正确
- 查看云函数日志

### 2. 后台管理应用连接失败

**解决方案**：
- 检查微信云开发配置
- 确认管理密钥正确
- 验证网络权限

### 3. 小程序授权问题

**解决方案**：
- 检查用户授权状态
- 重新获取用户信息
- 确认隐私设置

## 🤝 贡献指南

1. Fork 本仓库
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

### 提交规范

```bash
feat: 添加新功能
fix: 修复问题
docs: 更新文档
style: 代码格式化
refactor: 代码重构
test: 添加测试
chore: 构建过程或辅助工具的变动
```

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系我们

- **项目地址**: [GitHub Repository](https://github.com/your-username/mp-md)
- **问题反馈**: [GitHub Issues](https://github.com/your-username/mp-md/issues)
- **开发文档**: [Development Guide](./DEVELOPMENT_GUIDE.md)

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者！

---

**开始你的摸鱼记账之旅吧！** 🎣💰
